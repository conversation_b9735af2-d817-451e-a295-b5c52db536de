class_name SequenceNode extends CompositeNode

## Executes children in order until one fails. Returns SUCCESS only if all children succeed.
## Returns FAILURE as soon as any child fails.
## Returns RUNNING if any child is still running.

func _init(name: String = "Sequence", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in sequence until one fails or all succeed
	
	# Start from current child index (for RUNNING state continuity)
	for i in range(current_child_index, children.size()):
		var child = children[i]
		current_child_index = i
		
		var result = child.execute(ctx)
		
		match result:
			Status.FAILURE:
				# Child failed, sequence fails
				current_child_index = 0  # Reset for next execution
				return Status.FAILURE
			Status.RUNNING:
				# Child is still running, sequence is running
				return Status.RUNNING
			Status.SUCCESS:
				# Child succeeded, continue to next child
				continue
	
	# All children succeeded
	current_child_index = 0  # Reset for next execution
	return Status.SUCCESS
