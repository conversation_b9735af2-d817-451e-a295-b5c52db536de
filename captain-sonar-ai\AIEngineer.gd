extends Node
class_name AIEngineer

## AI Engineer Station - System Management and Damage Control
##
## The Engineer AI is responsible for:
## - System damage assessment and repair
## - Equipment maintenance and calibration
## - Resource management and optimization
## - Emergency response coordination
##
## Uses intelligent priority-based repair systems with emergency response
## capabilities and proactive maintenance scheduling.

signal surface_recommended(should_surface: bool)
signal system_repaired(system_name: String)
signal movement_recommendation(north: bool, south: bool, west: bool, east: bool)
signal emergency_repair_completed(system_name: String)
signal damage_report_updated(systems_status: Dictionary)

# System management
var systems_status: Dictionary = {}
var repair_queue: Array[String] = []
var maintenance_schedule: Dictionary = {}

# Damage control
var critical_systems: Array[String] = []
var emergency_repairs_active: bool = false
var repair_efficiency: float = 1.0

# Performance tracking
var repairs_completed: int = 0
var maintenance_cycles: int = 0
var system_uptime: Dictionary = {}

# Legacy compatibility
var ai_manager
var difficulty: AIProfile.Difficulty = AIProfile.Difficulty.NORMAL
var response_time: int = 20
var is_station_done: bool = true
var game_over: bool = false

func setup(manager: <PERSON><PERSON><PERSON><PERSON>, diff: AIProfile.Difficulty, response_ms: int) -> void:
	ai_manager = manager
	difficulty = diff
	response_time = response_ms
	_initialize_systems_management()

func _initialize_systems_management() -> void:
	## Initialize system management and damage control

	# Initialize systems status tracking
	systems_status = {
		"reactor": {"health": 4, "max_health": 4, "critical": true},
		"weapons": {"health": 4, "max_health": 4, "critical": true},
		"sonar": {"health": 4, "max_health": 4, "critical": false},
		"engine": {"health": 4, "max_health": 4, "critical": false},
		"silence": {"health": 6, "max_health": 6, "critical": false},
		"scenario": {"health": 1, "max_health": 1, "critical": false}
	}

	# Initialize critical systems list
	critical_systems = ["reactor", "weapons"]

	# Initialize system uptime tracking
	for system_name in systems_status:
		system_uptime[system_name] = 1.0

	print("AIEngineer: Systems management initialized")

func is_done() -> bool:
	return is_station_done

func set_game_over(is_over: bool) -> void:
	game_over = is_over

func start_turn() -> void:
	is_station_done = false
	_execute_engineer_turn()

func process_station() -> void:
	# Legacy method - redirect to start_turn for compatibility
	start_turn()

func _execute_engineer_turn() -> void:
	## Execute Engineer AI turn with system management focus

	# Update systems status
	_update_systems_status()

	# Check for emergency repairs needed
	_check_emergency_repairs()

	# Update repair priorities
	_update_repair_priorities()

	# Execute repair logic
	_execute_system_repairs()

	# Handle maintenance scheduling
	_handle_maintenance()

	# Provide recommendations
	_provide_recommendations()

	# Complete turn
	is_station_done = true

func _update_systems_status() -> void:
	## Update the status of all submarine systems

	if not ai_manager:
		return

	# Get current game state from manager
	var game_state = ai_manager.get_game_state() if ai_manager.has_method("get_game_state") else {}

	# Update system uptime tracking
	for system_name in systems_status:
		var system = systems_status[system_name]
		var uptime_ratio = float(system.health) / float(system.max_health)
		system_uptime[system_name] = uptime_ratio

func _check_emergency_repairs() -> void:
	## Check if emergency repairs are needed for critical systems

	emergency_repairs_active = false

	for system_name in critical_systems:
		if system_name in systems_status:
			var system = systems_status[system_name]
			if system.health <= 1:  # Critical damage
				emergency_repairs_active = true
				if system_name not in repair_queue:
					repair_queue.insert(0, system_name)  # Priority repair
				print("AIEngineer: Emergency repair needed for ", system_name)

func _update_repair_priorities() -> void:
	## Update system repair priorities based on current situation

	repair_queue.clear()

	# Get situation context
	var enemy_detected = false
	var in_combat = false

	if ai_manager and ai_manager.has_method("get_game_state"):
		var game_state = ai_manager.get_game_state()
		enemy_detected = game_state.get("enemy_detected", false)
		in_combat = game_state.get("in_combat", false)

	# Priority system based on situation
	var priorities: Array[Dictionary] = []

	for system_name in systems_status:
		var system = systems_status[system_name]
		var priority_score = 0

		# Base priority
		if system.critical:
			priority_score += 100
		else:
			priority_score += 50

		# Damage urgency
		var damage_level = system.max_health - system.health
		priority_score += damage_level * 20

		# Situation adjustments
		if in_combat or enemy_detected:
			match system_name:
				"weapons":
					priority_score += 50
				"reactor":
					priority_score += 40
				"sonar":
					priority_score += 30

		# Only add if damaged
		if system.health < system.max_health:
			priorities.append({
				"name": system_name,
				"priority": priority_score,
				"damage": damage_level
			})

	# Sort by priority (highest first)
	priorities.sort_custom(func(a, b): return a.priority > b.priority)

	# Build repair queue
	for item in priorities:
		repair_queue.append(item.name)

func _execute_system_repairs() -> void:
	## Execute system repairs based on priorities

	if repair_queue.is_empty():
		return

	# Repair highest priority system
	var system_to_repair = repair_queue[0]
	var system = systems_status[system_to_repair]

	if system.health < system.max_health:
		system.health += 1
		repairs_completed += 1

		system_repaired.emit(system_to_repair)

		if emergency_repairs_active and system_to_repair in critical_systems:
			emergency_repair_completed.emit(system_to_repair)

		print("AIEngineer: Repaired ", system_to_repair, " (", system.health, "/", system.max_health, ")")

		# Update repair efficiency
		_update_repair_efficiency()

func _handle_maintenance() -> void:
	## Handle proactive maintenance scheduling

	maintenance_cycles += 1

	# Schedule maintenance for systems with good health
	for system_name in systems_status:
		var system = systems_status[system_name]
		if system.health == system.max_health:
			if system_name not in maintenance_schedule:
				maintenance_schedule[system_name] = maintenance_cycles + 5  # Schedule future maintenance

func _provide_recommendations() -> void:
	## Provide tactical recommendations based on system status

	var total_health = 0
	var max_total_health = 0

	for system_name in systems_status:
		var system = systems_status[system_name]
		total_health += system.health
		max_total_health += system.max_health

	var health_ratio = float(total_health) / float(max_total_health)

	# Surface recommendation if heavily damaged
	if health_ratio < 0.4 or emergency_repairs_active:
		surface_recommended.emit(true)

	# Movement recommendations based on system status
	var can_move_safely = systems_status["engine"]["health"] >= 2
	var has_detection = systems_status["sonar"]["health"] >= 2

	if can_move_safely and has_detection:
		# Recommend cautious movement
		movement_recommendation.emit(true, true, true, true)
	elif can_move_safely:
		# Recommend limited movement
		movement_recommendation.emit(true, false, false, true)
	else:
		# Recommend no movement
		movement_recommendation.emit(false, false, false, false)

func _update_repair_efficiency() -> void:
	## Update repair efficiency based on performance

	var base_efficiency = 1.0

	# Efficiency based on emergency repairs
	if emergency_repairs_active:
		base_efficiency += 0.2  # Boost during emergencies

	# Efficiency based on maintenance cycles
	if maintenance_cycles > 0:
		base_efficiency += min(0.3, maintenance_cycles * 0.05)

	repair_efficiency = clamp(base_efficiency, 0.5, 2.0)

# Legacy compatibility methods
func get_system_status() -> Dictionary:
	return systems_status

func update_system_damage(system: String, is_damaged: bool) -> void:
	if system in systems_status:
		var system_data = systems_status[system]
		if is_damaged and system_data.health > 0:
			system_data.health -= 1
		elif not is_damaged and system_data.health < system_data.max_health:
			system_data.health += 1

		# Emit damage report update
		damage_report_updated.emit(systems_status)

func get_repair_priorities() -> Array:
	return repair_queue

func get_movement_recommendations() -> Array[bool]:
	# Return movement recommendations based on system status
	var can_move_safely = systems_status["engine"]["health"] >= 2
	var has_detection = systems_status["sonar"]["health"] >= 2

	if can_move_safely and has_detection:
		return [true, true, true, true]
	elif can_move_safely:
		return [true, false, false, true]
	else:
		return [false, false, false, false]

func update_captain_wants(guns: bool, detection: bool, silence: bool) -> void:
	# Update internal tracking of captain's priorities
	if guns and "weapons" not in repair_queue and systems_status["weapons"]["health"] < systems_status["weapons"]["max_health"]:
		repair_queue.insert(0, "weapons")

	if detection and "sonar" not in repair_queue and systems_status["sonar"]["health"] < systems_status["sonar"]["max_health"]:
		repair_queue.insert(1, "sonar")

# Debug methods
func get_debug_info() -> Dictionary:
	return {
		"station": "engineer",
		"difficulty": difficulty,
		"is_done": is_station_done,
		"systems_status": systems_status,
		"repair_queue": repair_queue,
		"emergency_repairs_active": emergency_repairs_active,
		"repair_efficiency": repair_efficiency,
		"repairs_completed": repairs_completed,
		"maintenance_cycles": maintenance_cycles,
		"system_uptime": system_uptime
	}
