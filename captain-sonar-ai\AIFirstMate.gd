extends Node
class_name AIFirstMate

## AI FirstMate Station - Equipment Management and Tactical Support
##
## The FirstMate AI is responsible for:
## - Equipment charging and management
## - Weapon system preparation
## - Tactical support coordination
## - Combat readiness assessment
##
## Uses behavior trees for intelligent decision making with priority-based
## equipment charging and tactical coordination.

signal equipment_ready(equipment_name: String)
signal charging_priority_changed(equipment_name: String, priority: int)
signal tactical_support_activated(support_type: String)

# Equipment management
var equipment_status: Dictionary = {}
var charging_queue: Array[String] = []
var tactical_systems: Dictionary = {}

# Combat readiness
var combat_readiness: float = 0.0
var weapon_systems_ready: bool = false
var support_systems_ready: bool = false

# Performance tracking
var equipment_efficiency: float = 1.0
var charging_cycles_completed: int = 0

# Legacy compatibility
var ai_manager
var difficulty: AIProfile.Difficulty = AIProfile.Difficulty.NORMAL
var response_time: int = 20
var is_station_done: bool = true
var game_over: bool = false

func setup(manager: <PERSON><PERSON><PERSON><PERSON>, diff: AIProfile.Difficulty, response_ms: int) -> void:
	ai_manager = manager
	difficulty = diff
	response_time = response_ms
	_initialize_equipment_systems()

func _initialize_equipment_systems() -> void:
	## Initialize equipment management systems

	# Initialize equipment status tracking
	equipment_status = {
		"torpedo": {"current": 0, "max": 4, "priority": 100},
		"mine": {"current": 0, "max": 3, "priority": 90},
		"sonar": {"current": 0, "max": 3, "priority": 80},
		"drone": {"current": 0, "max": 4, "priority": 70},
		"silence": {"current": 0, "max": 6, "priority": 60},
		"scenario": {"current": 0, "max": 1, "priority": 50}
	}

	# Initialize tactical systems
	tactical_systems = {
		"weapon_coordination": false,
		"detection_support": false,
		"stealth_assistance": false
	}

	print("AIFirstMate: Equipment systems initialized")

func is_done() -> bool:
	return is_station_done

func set_game_over(is_over: bool) -> void:
	game_over = is_over

func start_turn() -> void:
	is_station_done = false
	_execute_firstmate_turn()

func process_station() -> void:
	# Legacy method - redirect to start_turn for compatibility
	start_turn()

func _execute_firstmate_turn() -> void:
	## Execute FirstMate AI turn with equipment management focus

	# Update equipment status
	_update_equipment_status()

	# Calculate combat readiness
	_calculate_combat_readiness()

	# Update charging priorities based on situation
	_update_charging_priorities()

	# Execute equipment charging logic
	_execute_equipment_charging()

	# Handle tactical support
	_handle_tactical_support()

	# Complete turn
	is_station_done = true

func _update_equipment_status() -> void:
	## Update the status of all equipment systems

	if not ai_manager:
		return

	# Get current game state from manager
	var game_state = ai_manager.get_game_state() if ai_manager.has_method("get_game_state") else {}

	# Update equipment efficiency based on usage patterns
	_update_equipment_efficiency()

func _calculate_combat_readiness() -> void:
	## Calculate overall combat readiness based on equipment status

	var total_readiness = 0.0
	var equipment_count = 0

	# Check weapon systems
	var weapon_readiness = 0.0
	for weapon in ["torpedo", "mine"]:
		if weapon in equipment_status:
			var status = equipment_status[weapon]
			weapon_readiness += float(status.current) / float(status.max)
			equipment_count += 1

	weapon_systems_ready = weapon_readiness >= 1.0

	# Check support systems
	var support_readiness = 0.0
	for support in ["drone", "sonar", "silence"]:
		if support in equipment_status:
			var status = equipment_status[support]
			support_readiness += float(status.current) / float(status.max)
			equipment_count += 1

	support_systems_ready = support_readiness >= 2.0

	# Calculate overall readiness
	total_readiness = (weapon_readiness + support_readiness) / max(1, equipment_count)
	combat_readiness = clamp(total_readiness, 0.0, 1.0)

func _update_charging_priorities() -> void:
	## Update equipment charging priorities based on current situation

	charging_queue.clear()

	# Get situation context
	var enemy_detected = false
	var our_health = 4

	if ai_manager and ai_manager.has_method("get_game_state"):
		var game_state = ai_manager.get_game_state()
		enemy_detected = game_state.get("enemy_detected", false)
		our_health = game_state.get("our_health", 4)

	# Priority system based on situation
	var priorities: Array[Dictionary] = []

	for equipment_name in equipment_status:
		var status = equipment_status[equipment_name]
		var priority_score = status.priority

		# Adjust priority based on situation
		if enemy_detected:
			match equipment_name:
				"torpedo", "mine":
					priority_score += 50  # High priority for weapons in combat
				"sonar":
					priority_score += 30  # Medium priority for detection

		if our_health <= 2:
			match equipment_name:
				"silence":
					priority_score += 40  # High priority for stealth when damaged

		# Only add if not fully charged
		if status.current < status.max:
			priorities.append({
				"name": equipment_name,
				"priority": priority_score,
				"urgency": status.max - status.current
			})

	# Sort by priority (highest first)
	priorities.sort_custom(func(a, b): return a.priority > b.priority)

	# Build charging queue
	for item in priorities:
		charging_queue.append(item.name)

func _execute_equipment_charging() -> void:
	## Execute equipment charging based on priorities

	if charging_queue.is_empty():
		return

	# Charge highest priority equipment
	var equipment_to_charge = charging_queue[0]
	var status = equipment_status[equipment_to_charge]

	if status.current < status.max:
		status.current += 1
		equipment_ready.emit(equipment_to_charge)

		# Emit priority change for next items
		for i in range(min(3, charging_queue.size())):
			var equipment_name = charging_queue[i]
			charging_priority_changed.emit(equipment_name, i + 1)

		print("AIFirstMate: Charged ", equipment_to_charge, " (", status.current, "/", status.max, ")")

func _handle_tactical_support() -> void:
	## Handle tactical support coordination

	# Activate tactical support based on readiness and situation
	if combat_readiness > 0.7:
		if not tactical_systems["weapon_coordination"]:
			tactical_systems["weapon_coordination"] = true
			tactical_support_activated.emit("weapon_coordination")

	if equipment_efficiency > 0.8:
		if not tactical_systems["detection_support"]:
			tactical_systems["detection_support"] = true
			tactical_support_activated.emit("detection_support")

func _update_equipment_efficiency() -> void:
	## Update equipment efficiency based on performance

	var total_efficiency = 0.0
	var equipment_count = 0

	for equipment_name in equipment_status:
		var status = equipment_status[equipment_name]
		var efficiency = 1.0

		# Calculate efficiency based on charge level and usage
		if status.max > 0:
			efficiency = float(status.current) / float(status.max)

		total_efficiency += efficiency
		equipment_count += 1

	if equipment_count > 0:
		equipment_efficiency = total_efficiency / equipment_count

	charging_cycles_completed += 1

# Legacy compatibility methods
func get_equipment_status() -> Dictionary:
	return equipment_status

func update_equipment_status(equipment: String, current: int, max_val: int) -> void:
	if equipment in equipment_status:
		equipment_status[equipment]["current"] = current
		equipment_status[equipment]["max"] = max_val

func get_charging_priorities() -> Array:
	return charging_queue

func start_charging_work(uses: int) -> void:
	# Legacy method for starting charging work
	_execute_equipment_charging()

# Debug methods
func get_debug_info() -> Dictionary:
	return {
		"station": "firstmate",
		"difficulty": difficulty,
		"is_done": is_station_done,
		"equipment_status": equipment_status,
		"charging_queue": charging_queue,
		"combat_readiness": combat_readiness,
		"weapon_systems_ready": weapon_systems_ready,
		"support_systems_ready": support_systems_ready,
		"equipment_efficiency": equipment_efficiency,
		"charging_cycles_completed": charging_cycles_completed
	}

	return base_info
