extends Node
class_name AIPlayerModeler

## AI Player Modeling System
##
## This system builds and maintains a statistical model of player behavior
## for predictive analysis and strategic planning. Used by hard mode AI
## to adapt tactics based on observed player patterns.

signal model_updated(model: Dictionary)
signal adaptation_triggered(adaptation: Dictionary)

# Player behavior model
var player_model: Dictionary = {
	"movement_preferences": {},
	"reaction_times": [],
	"strategy_effectiveness": {},
	"common_mistakes": [],
	"skill_indicators": {},
	"adaptation_rate": 0.0
}

# Learning parameters
var learning_rate: float = 0.1
var memory_decay: float = 0.95
var adaptation_threshold: float = 0.3
var max_memory_size: int = 100

# Model state
var observations_count: int = 0
var model_confidence: float = 0.0
var last_adaptation: float = 0.0

# Performance tracking
var successful_adaptations: int = 0
var total_adaptations: int = 0

func _init():
	name = "AIPlayerModeler"
	_initialize_model()

func _initialize_model() -> void:
	## Initialize the player behavior model
	
	player_model = {
		"movement_preferences": {
			"north": 0.25,
			"south": 0.25,
			"east": 0.25,
			"west": 0.25
		},
		"reaction_times": [],
		"strategy_effectiveness": {
			"aggressive": 0.5,
			"defensive": 0.5,
			"stealth": 0.5,
			"balanced": 0.5
		},
		"common_mistakes": [],
		"skill_indicators": {
			"tactical_awareness": 0.5,
			"resource_management": 0.5,
			"timing_precision": 0.5,
			"adaptability": 0.5
		},
		"adaptation_rate": 0.0,
		"confidence_level": 0.0
	}

func update_model(game_state: Dictionary) -> void:
	## Update the player model with new observations
	
	observations_count += 1
	
	# Update movement preferences
	_update_movement_preferences(game_state)
	
	# Update reaction times
	_update_reaction_times(game_state)
	
	# Update strategy effectiveness
	_update_strategy_effectiveness(game_state)
	
	# Update skill indicators
	_update_skill_indicators(game_state)
	
	# Detect common mistakes
	_detect_mistakes(game_state)
	
	# Update model confidence
	_update_model_confidence()
	
	# Check for adaptation triggers
	_check_adaptation_triggers()
	
	# Emit model update
	model_updated.emit(player_model)

func predict_player_actions(context: Dictionary) -> Dictionary:
	## Predict likely player actions based on the model
	
	var predictions = {}
	
	# Predict movement
	predictions["movement"] = _predict_movement(context)
	
	# Predict strategy
	predictions["strategy"] = _predict_strategy(context)
	
	# Predict reaction time
	predictions["reaction_time"] = _predict_reaction_time()
	
	# Predict mistakes
	predictions["likely_mistakes"] = _predict_mistakes(context)
	
	return predictions

func predict_sequence(current_state: Dictionary, turns_ahead: int) -> Array[Dictionary]:
	## Predict a sequence of player actions for multiple turns
	
	var predictions: Array[Dictionary] = []
	var simulated_state = current_state.duplicate(true)
	
	for turn in range(turns_ahead):
		var turn_prediction = predict_player_actions(simulated_state)
		predictions.append(turn_prediction)
		
		# Update simulated state based on prediction
		simulated_state = _simulate_state_update(simulated_state, turn_prediction)
	
	return predictions

func update_from_validation(predicted: Dictionary, actual: Dictionary, accuracy: float) -> void:
	## Update model based on prediction validation
	
	# Adjust learning rate based on accuracy
	var adjusted_learning_rate = learning_rate * (1.0 + (accuracy - 0.5))
	
	# Update movement preferences if movement was predicted
	if predicted.has("movement") and actual.has("movement_direction"):
		_adjust_movement_model(predicted.movement, actual.movement_direction, adjusted_learning_rate)
	
	# Update strategy effectiveness
	if predicted.has("strategy") and actual.has("strategy_used"):
		_adjust_strategy_model(predicted.strategy, actual.strategy_used, accuracy)
	
	# Update skill indicators based on performance
	_adjust_skill_indicators(accuracy)

func get_current_model() -> Dictionary:
	## Get the current player behavior model
	
	return player_model.duplicate(true)

func get_status() -> Dictionary:
	## Get player modeler status
	
	return {
		"observations_count": observations_count,
		"model_confidence": model_confidence,
		"successful_adaptations": successful_adaptations,
		"total_adaptations": total_adaptations,
		"adaptation_success_rate": _get_adaptation_success_rate(),
		"model_summary": _get_model_summary()
	}

func _update_movement_preferences(game_state: Dictionary) -> void:
	## Update movement preference statistics
	
	var movement = game_state.get("enemy_movement", "")
	if movement == "":
		return
	
	# Normalize movement preferences
	var total_moves = 0.0
	for direction in player_model.movement_preferences:
		total_moves += player_model.movement_preferences[direction]
	
	# Add new observation
	if movement in player_model.movement_preferences:
		player_model.movement_preferences[movement] += learning_rate
		total_moves += learning_rate
		
		# Renormalize
		for direction in player_model.movement_preferences:
			player_model.movement_preferences[direction] /= total_moves

func _update_reaction_times(game_state: Dictionary) -> void:
	## Update reaction time statistics
	
	var reaction_time = game_state.get("enemy_response_time", 0.0)
	if reaction_time <= 0:
		return
	
	player_model.reaction_times.append(reaction_time)
	
	# Maintain memory size
	if player_model.reaction_times.size() > max_memory_size:
		player_model.reaction_times.pop_front()

func _update_strategy_effectiveness(game_state: Dictionary) -> void:
	## Update strategy effectiveness based on outcomes
	
	var strategy_used = game_state.get("enemy_strategy", "")
	var outcome_success = game_state.get("enemy_success", false)
	
	if strategy_used == "" or not player_model.strategy_effectiveness.has(strategy_used):
		return
	
	# Update effectiveness with exponential moving average
	var current_effectiveness = player_model.strategy_effectiveness[strategy_used]
	var success_value = 1.0 if outcome_success else 0.0
	
	player_model.strategy_effectiveness[strategy_used] = current_effectiveness * (1.0 - learning_rate) + success_value * learning_rate

func _update_skill_indicators(game_state: Dictionary) -> void:
	## Update skill indicator assessments
	
	# Tactical awareness based on optimal moves
	var optimal_move = game_state.get("optimal_move", "")
	var actual_move = game_state.get("enemy_movement", "")
	
	if optimal_move != "" and actual_move != "":
		var tactical_score = 1.0 if optimal_move == actual_move else 0.0
		_update_skill_indicator("tactical_awareness", tactical_score)
	
	# Resource management based on system usage
	var systems_used = game_state.get("enemy_systems_used", [])
	var systems_available = game_state.get("enemy_systems_available", [])
	
	if systems_available.size() > 0:
		var usage_efficiency = float(systems_used.size()) / float(systems_available.size())
		_update_skill_indicator("resource_management", usage_efficiency)
	
	# Timing precision based on response time consistency
	if player_model.reaction_times.size() >= 5:
		var avg_time = _calculate_average_reaction_time()
		var variance = _calculate_reaction_time_variance(avg_time)
		var precision = 1.0 - min(1.0, variance / max(avg_time, 1.0))
		_update_skill_indicator("timing_precision", precision)

func _detect_mistakes(game_state: Dictionary) -> void:
	## Detect and record common player mistakes
	
	var mistakes = []
	
	# Detect movement into danger
	if game_state.get("moved_into_danger", false):
		mistakes.append("moved_into_danger")
	
	# Detect inefficient resource usage
	if game_state.get("wasted_resources", false):
		mistakes.append("wasted_resources")
	
	# Detect predictable patterns
	if game_state.get("predictable_behavior", false):
		mistakes.append("predictable_behavior")
	
	# Add new mistakes to model
	for mistake in mistakes:
		if mistake not in player_model.common_mistakes:
			player_model.common_mistakes.append(mistake)
		
		# Limit mistake history
		if player_model.common_mistakes.size() > 20:
			player_model.common_mistakes.pop_front()

func _update_model_confidence() -> void:
	## Update overall model confidence based on observations
	
	# Confidence increases with more observations but has diminishing returns
	var base_confidence = min(1.0, float(observations_count) / 50.0)
	
	# Adjust based on consistency of observations
	var consistency_bonus = _calculate_consistency_bonus()
	
	model_confidence = min(1.0, base_confidence + consistency_bonus)
	player_model.confidence_level = model_confidence

func _check_adaptation_triggers() -> void:
	## Check if adaptation should be triggered
	
	var current_time = Time.get_unix_time_from_system()
	
	# Don't adapt too frequently
	if current_time - last_adaptation < 30.0:  # 30 seconds minimum
		return
	
	# Check if player behavior has changed significantly
	var behavior_change = _detect_behavior_change()
	
	if behavior_change > adaptation_threshold:
		_trigger_adaptation(behavior_change)
		last_adaptation = current_time

func _predict_movement(context: Dictionary) -> String:
	## Predict most likely movement direction
	
	var best_direction = ""
	var best_probability = 0.0
	
	for direction in player_model.movement_preferences:
		var probability = player_model.movement_preferences[direction]
		
		# Adjust probability based on context
		probability = _adjust_movement_probability(direction, context, probability)
		
		if probability > best_probability:
			best_probability = probability
			best_direction = direction
	
	return best_direction

func _predict_strategy(context: Dictionary) -> String:
	## Predict most likely strategy
	
	var best_strategy = ""
	var best_effectiveness = 0.0
	
	for strategy in player_model.strategy_effectiveness:
		var effectiveness = player_model.strategy_effectiveness[strategy]
		
		# Adjust based on context
		effectiveness = _adjust_strategy_probability(strategy, context, effectiveness)
		
		if effectiveness > best_effectiveness:
			best_effectiveness = effectiveness
			best_strategy = strategy
	
	return best_strategy

func _predict_reaction_time() -> float:
	## Predict expected reaction time
	
	if player_model.reaction_times.is_empty():
		return 2.0  # Default assumption
	
	return _calculate_average_reaction_time()

func _predict_mistakes(context: Dictionary) -> Array:
	## Predict likely mistakes based on context
	
	var likely_mistakes = []
	
	# Check if context makes certain mistakes more likely
	for mistake in player_model.common_mistakes:
		var likelihood = _calculate_mistake_likelihood(mistake, context)
		if likelihood > 0.3:  # 30% threshold
			likely_mistakes.append({
				"mistake": mistake,
				"likelihood": likelihood
			})
	
	return likely_mistakes

func _simulate_state_update(state: Dictionary, prediction: Dictionary) -> Dictionary:
	## Simulate state update based on prediction

	var new_state = state.duplicate(true)

	# Update position based on predicted movement
	if prediction.has("movement") and prediction.movement != "":
		var current_pos = state.get("enemy_position", Vector2i.ZERO)
		match prediction.movement:
			"north":
				new_state["enemy_position"] = current_pos + Vector2i(0, -1)
			"south":
				new_state["enemy_position"] = current_pos + Vector2i(0, 1)
			"east":
				new_state["enemy_position"] = current_pos + Vector2i(1, 0)
			"west":
				new_state["enemy_position"] = current_pos + Vector2i(-1, 0)

	# Update turn count
	new_state["turn_count"] = state.get("turn_count", 0) + 1

	return new_state

func _adjust_movement_model(predicted: String, actual: String, learning_rate: float) -> void:
	## Adjust movement model based on prediction accuracy

	if predicted == actual:
		# Strengthen correct prediction
		if predicted in player_model.movement_preferences:
			player_model.movement_preferences[predicted] += learning_rate * 0.1
	else:
		# Weaken incorrect prediction, strengthen actual
		if predicted in player_model.movement_preferences:
			player_model.movement_preferences[predicted] -= learning_rate * 0.05
		if actual in player_model.movement_preferences:
			player_model.movement_preferences[actual] += learning_rate * 0.1

	# Renormalize
	var total = 0.0
	for direction in player_model.movement_preferences:
		total += max(0.01, player_model.movement_preferences[direction])  # Minimum value

	for direction in player_model.movement_preferences:
		player_model.movement_preferences[direction] = max(0.01, player_model.movement_preferences[direction]) / total

func _adjust_strategy_model(predicted: String, actual: String, accuracy: float) -> void:
	## Adjust strategy model based on prediction accuracy

	if predicted in player_model.strategy_effectiveness:
		# Update effectiveness based on how well we predicted it
		var current = player_model.strategy_effectiveness[predicted]
		player_model.strategy_effectiveness[predicted] = current * (1.0 - learning_rate) + accuracy * learning_rate

func _adjust_skill_indicators(accuracy: float) -> void:
	## Adjust skill indicators based on prediction accuracy

	# If we're predicting well, the player might be more predictable (lower adaptability)
	var adaptability_adjustment = -accuracy * learning_rate * 0.1
	_update_skill_indicator("adaptability", player_model.skill_indicators["adaptability"] + adaptability_adjustment)

func _update_skill_indicator(indicator: String, value: float) -> void:
	## Update a specific skill indicator with exponential moving average

	if indicator in player_model.skill_indicators:
		var current = player_model.skill_indicators[indicator]
		player_model.skill_indicators[indicator] = current * (1.0 - learning_rate) + clamp(value, 0.0, 1.0) * learning_rate

func _calculate_average_reaction_time() -> float:
	## Calculate average reaction time

	if player_model.reaction_times.is_empty():
		return 2.0

	var sum = 0.0
	for time in player_model.reaction_times:
		sum += time

	return sum / float(player_model.reaction_times.size())

func _calculate_reaction_time_variance(average: float) -> float:
	## Calculate reaction time variance

	if player_model.reaction_times.size() < 2:
		return 0.0

	var sum_squared_diff = 0.0
	for time in player_model.reaction_times:
		var diff = time - average
		sum_squared_diff += diff * diff

	return sum_squared_diff / float(player_model.reaction_times.size())

func _calculate_consistency_bonus() -> float:
	## Calculate consistency bonus for model confidence

	var bonus = 0.0

	# Movement consistency
	var movement_entropy = _calculate_movement_entropy()
	bonus += (1.0 - movement_entropy) * 0.2

	# Reaction time consistency
	if player_model.reaction_times.size() >= 5:
		var avg_time = _calculate_average_reaction_time()
		var variance = _calculate_reaction_time_variance(avg_time)
		var consistency = 1.0 - min(1.0, variance / max(avg_time, 1.0))
		bonus += consistency * 0.1

	return clamp(bonus, 0.0, 0.3)

func _calculate_movement_entropy() -> float:
	## Calculate entropy of movement preferences (lower = more predictable)

	var entropy = 0.0

	for direction in player_model.movement_preferences:
		var probability = player_model.movement_preferences[direction]
		if probability > 0:
			entropy -= probability * log(probability) / log(2)

	# Normalize to 0-1 range (max entropy for 4 directions is 2)
	return entropy / 2.0

func _detect_behavior_change() -> float:
	## Detect significant changes in player behavior

	# This is a simplified implementation
	# In a full implementation, you'd compare recent behavior to historical patterns

	var change_score = 0.0

	# Check if recent movements differ from historical preferences
	if observations_count > 20:
		# Compare last 10 observations to overall model
		# This would require storing recent observations separately
		change_score = 0.1  # Placeholder

	return change_score

func _trigger_adaptation(change_magnitude: float) -> void:
	## Trigger adaptation to changed player behavior

	total_adaptations += 1

	var adaptation = {
		"timestamp": Time.get_unix_time_from_system(),
		"change_magnitude": change_magnitude,
		"adaptation_type": "behavior_shift",
		"adjustments_made": []
	}

	# Increase learning rate temporarily for faster adaptation
	var old_learning_rate = learning_rate
	learning_rate = min(0.3, learning_rate * 2.0)
	adaptation.adjustments_made.append("increased_learning_rate")

	# Reset some confidence to allow for new learning
	model_confidence *= 0.8
	adaptation.adjustments_made.append("reduced_confidence")

	# Emit adaptation signal
	adaptation_triggered.emit(adaptation)

	# Schedule learning rate reset (in a real implementation, you'd use a timer)
	# For now, we'll reset it after a few updates
	call_deferred("_reset_learning_rate", old_learning_rate)

func _reset_learning_rate(original_rate: float) -> void:
	## Reset learning rate after adaptation period

	learning_rate = original_rate

func _adjust_movement_probability(direction: String, context: Dictionary, base_probability: float) -> float:
	## Adjust movement probability based on context

	var adjusted = base_probability

	# Adjust based on danger in that direction
	if context.has("danger_directions") and direction in context.danger_directions:
		adjusted *= 0.5  # Less likely to move into danger

	# Adjust based on objectives in that direction
	if context.has("objective_directions") and direction in context.objective_directions:
		adjusted *= 1.5  # More likely to move toward objectives

	return clamp(adjusted, 0.01, 1.0)

func _adjust_strategy_probability(strategy: String, context: Dictionary, base_effectiveness: float) -> float:
	## Adjust strategy probability based on context

	var adjusted = base_effectiveness

	# Adjust based on current situation
	var our_health = context.get("our_health", 4)
	var enemy_health = context.get("enemy_health", 4)

	match strategy:
		"aggressive":
			if our_health > enemy_health:
				adjusted *= 1.2  # More likely when we have advantage
			else:
				adjusted *= 0.8
		"defensive":
			if our_health < enemy_health:
				adjusted *= 1.2  # More likely when we're behind
			else:
				adjusted *= 0.8

	return clamp(adjusted, 0.0, 1.0)

func _calculate_mistake_likelihood(mistake: String, context: Dictionary) -> float:
	## Calculate likelihood of a specific mistake in given context

	var base_likelihood = 0.2  # Base 20% chance

	match mistake:
		"moved_into_danger":
			if context.has("danger_nearby") and context.danger_nearby:
				base_likelihood *= 2.0
		"wasted_resources":
			if context.has("resources_low") and context.resources_low:
				base_likelihood *= 1.5
		"predictable_behavior":
			# More likely if player has been predictable recently
			base_likelihood *= (1.0 - player_model.skill_indicators.get("adaptability", 0.5))

	return clamp(base_likelihood, 0.0, 1.0)

func _get_adaptation_success_rate() -> float:
	## Get adaptation success rate

	if total_adaptations == 0:
		return 0.0

	return float(successful_adaptations) / float(total_adaptations)

func _get_model_summary() -> Dictionary:
	## Get a summary of the current model

	return {
		"dominant_movement": _get_dominant_movement(),
		"preferred_strategy": _get_preferred_strategy(),
		"average_reaction_time": _calculate_average_reaction_time(),
		"skill_level": _calculate_overall_skill_level(),
		"predictability": 1.0 - player_model.skill_indicators.get("adaptability", 0.5)
	}

func _get_dominant_movement() -> String:
	## Get the most preferred movement direction

	var best_direction = ""
	var best_probability = 0.0

	for direction in player_model.movement_preferences:
		if player_model.movement_preferences[direction] > best_probability:
			best_probability = player_model.movement_preferences[direction]
			best_direction = direction

	return best_direction

func _get_preferred_strategy() -> String:
	## Get the most effective strategy for this player

	var best_strategy = ""
	var best_effectiveness = 0.0

	for strategy in player_model.strategy_effectiveness:
		if player_model.strategy_effectiveness[strategy] > best_effectiveness:
			best_effectiveness = player_model.strategy_effectiveness[strategy]
			best_strategy = strategy

	return best_strategy

func _calculate_overall_skill_level() -> float:
	## Calculate overall skill level from indicators

	var total_skill = 0.0
	var indicator_count = 0

	for indicator in player_model.skill_indicators:
		total_skill += player_model.skill_indicators[indicator]
		indicator_count += 1

	if indicator_count == 0:
		return 0.5

	return total_skill / float(indicator_count)
