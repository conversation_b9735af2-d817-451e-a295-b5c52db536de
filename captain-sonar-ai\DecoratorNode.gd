class_name DecoratorNode extends BehaviorNode

## Base class for nodes that modify the behavior of a single child node

var child: BehaviorNode

func _init(name: String = "", child_node: BehaviorNode = null) -> void:
	super(name)
	child = child_node

func set_child(child_node: <PERSON>haviorNode) -> void:
	## Set the child node for this decorator
	child = child_node

func reset() -> void:
	## Reset this node and its child
	super.reset()
	if child:
		child.reset()
