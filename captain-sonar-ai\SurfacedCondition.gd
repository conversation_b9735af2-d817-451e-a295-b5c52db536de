class_name SurfacedCondition extends ConditionNode

## Checks if submarine is currently surfaced

func _init(name: String = "Surfaced") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Check if submarine is surfaced
	if ctx.is_surfaced:
		return Status.SUCCESS
	else:
		return Status.FAILURE
## Checks if submarine is currently surfaced

func _init() -> void:
	super("Surfaced")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	return Status.SUCCESS if ctx.is_surfaced else Status.FAILURE
