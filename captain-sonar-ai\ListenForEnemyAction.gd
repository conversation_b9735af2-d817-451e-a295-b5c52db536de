extends ActionNode
class_name ListenForEnemyAction

## Listen For Enemy Action
##
## Action to listen for enemy activity

func _init() -> void:
	super("ListenForEnemy")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("ListenForEnemyAction: Listening for enemy activity")
	
	ctx.emit_event("listening_for_enemy", {
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
