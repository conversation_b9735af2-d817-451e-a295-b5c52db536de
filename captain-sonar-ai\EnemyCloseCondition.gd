extends ConditionNode
class_name EnemyCloseCondition

## Enemy Close Condition
##
## Checks if enemy is close to our position

var close_distance: float = 3.0

func _init(distance: float = 3.0) -> void:
	super("EnemyClose")
	close_distance = distance

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.is_enemy_detected():
		return Status.FAILURE
	
	var distance = ctx.submarine_position.distance_to(ctx.enemy_position)
	return Status.SUCCESS if distance <= close_distance else Status.FAILURE
