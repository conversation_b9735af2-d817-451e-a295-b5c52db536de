extends ActionNode
class_name EvadeAction

## Evade Action
##
## Action to evade from enemy position

func _init() -> void:
	super("Evade")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.is_enemy_detected():
		return Status.FAILURE
	
	# Calculate evasion direction (away from enemy)
	var current_pos = ctx.submarine_position
	var enemy_pos = ctx.enemy_position
	
	var direction = ""
	var dx = current_pos.x - enemy_pos.x
	var dy = current_pos.y - enemy_pos.y
	
	# Choose direction that moves away from enemy
	if abs(dx) > abs(dy):
		direction = "EAST" if dx > 0 else "WEST"
	else:
		direction = "SOUTH" if dy > 0 else "NORTH"
	
	if debug_enabled:
		print("EvadeAction: Evading ", direction, " from enemy at ", enemy_pos)
	
	ctx.emit_event("movement_decision", {"direction": direction})
	return Status.SUCCESS
