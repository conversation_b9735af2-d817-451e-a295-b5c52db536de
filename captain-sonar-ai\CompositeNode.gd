class_name CompositeNode extends Be<PERSON>viorNode

## Base class for nodes that have child nodes (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lle<PERSON>)

var children: Array[<PERSON>haviorNode] = []
var current_child_index: int = 0

func _init(name: String = "", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name)
	children = child_nodes

func add_child(child: BehaviorNode) -> void:
	## Add a child node to this composite
	children.append(child)

func remove_child(child: BehaviorNode) -> void:
	## Remove a child node from this composite
	children.erase(child)

func get_child_count() -> int:
	## Get the number of child nodes
	return children.size()

func reset() -> void:
	## Reset this node and all children
	super.reset()
	current_child_index = 0
	for child in children:
		child.reset()
