extends Node
class_name <PERSON><PERSON>ontroll<PERSON>

## Central AI Controller Singleton
## 
## This singleton manages the entire AI system using event-driven architecture,
## behavior trees, and parameter-based difficulty scaling. It replaces the old
## AIManager with a cleaner, more maintainable design.
##
## Key Features:
## - Event-driven communication between stations
## - Behavior tree-based decision making
## - Parameter-based difficulty instead of separate implementations
## - Clean separation of concerns
## - Testable components

signal ai_turn_completed
signal ai_wants_to_surface
signal ai_movement_decision(direction: String)
signal ai_action_completed(station_type: String, action: String)

# Singleton instance
static var _instance: AIController = null

## Core system components
var ai_personality: AIPersonality
var ai_event_bus: AIEventBus
var ai_behavior_manager: AIBehaviorManager

## Hard mode systems (Phase 3)
var hard_mode_manager: AIHardModeManager

## AI Stations - now inherit from AIStation base class
var captain_ai: AICaptain
var engineer_ai: AIEngineer
var first_mate_ai: AIFirstMate
var radio_operator_ai: AIRadioOperator

## System state
var team_id: String = "bravo"
var is_initialized: bool = false
var debug_mode: bool = false
var current_turn: int = 0

## Game state tracking
var submarine_position: Vector2i = Vector2i.ZERO
var submarine_health: int = 4
var is_surfaced: bool = false

static func get_instance() -> AIController:
	if _instance == null:
		_instance = AIController.new()
		# Add to the scene tree if needed
		if Engine.get_main_loop() and Engine.get_main_loop().current_scene:
			Engine.get_main_loop().current_scene.add_child(_instance)
	return _instance

func _ready() -> void:
	# Set up singleton
	if _instance == null:
		_instance = self
	elif _instance != self:
		queue_free()
		return

	# Ensure this is a singleton
	if not _is_singleton_ready():
		_setup_singleton()

	_initialize_core_systems()
	_initialize_ai_stations()
	_connect_station_signals()

	is_initialized = true

	if debug_mode:
		print("AIController: Initialization complete for team ", team_id)

func _is_singleton_ready() -> bool:
	## Check if this instance should be the singleton
	return get_tree().get_first_node_in_group("ai_controller") == self

func _setup_singleton() -> void:
	## Setup this node as the AI controller singleton
	add_to_group("ai_controller")
	process_mode = Node.PROCESS_MODE_ALWAYS

func _initialize_core_systems() -> void:
	## Initialize the core AI systems
	
	# Create AI personality with default parameters
	ai_personality = AIPersonality.new()
	
	# Create event bus for inter-station communication
	ai_event_bus = AIEventBus.new()
	add_child(ai_event_bus)
	
	# Create behavior manager for coordinating behavior trees
	ai_behavior_manager = AIBehaviorManager.new()
	ai_behavior_manager.setup(ai_personality, ai_event_bus)
	add_child(ai_behavior_manager)

	# Initialize hard mode systems if enabled
	if ai_personality.strategic_planning_enabled:
		hard_mode_manager = AIHardModeManager.new()
		add_child(hard_mode_manager)
		hard_mode_manager.initialize_hard_mode(ai_personality)

		if debug_mode:
			print("AIController: Hard mode systems initialized")

	if debug_mode:
		print("AIController: Core systems initialized")

func _initialize_ai_stations() -> void:
	## Create and initialize all AI stations
	
	# Create station instances
	captain_ai = AICaptain.new()
	engineer_ai = AIEngineer.new()
	first_mate_ai = AIFirstMate.new()
	radio_operator_ai = AIRadioOperator.new()
	
	# Add them as children
	add_child(captain_ai)
	add_child(engineer_ai)
	add_child(first_mate_ai)
	add_child(radio_operator_ai)
	
	# Setup each station with shared systems
	captain_ai.setup(ai_personality, ai_event_bus, ai_behavior_manager)
	engineer_ai.setup(ai_personality, ai_event_bus, ai_behavior_manager)
	first_mate_ai.setup(ai_personality, ai_event_bus, ai_behavior_manager)
	radio_operator_ai.setup(ai_personality, ai_event_bus, ai_behavior_manager)
	
	if debug_mode:
		print("AIController: All stations initialized")

func _connect_station_signals() -> void:
	## Connect station signals to controller handlers
	
	# Captain signals
	captain_ai.movement_decision.connect(_on_captain_movement_decision)
	captain_ai.combat_action.connect(_on_captain_combat_action)
	
	# Engineer signals
	engineer_ai.surface_recommended.connect(_on_engineer_surface_recommendation)
	engineer_ai.system_repaired.connect(_on_engineer_system_repaired)
	
	# First Mate signals
	first_mate_ai.equipment_ready.connect(_on_first_mate_equipment_ready)
	first_mate_ai.equipment_used.connect(_on_first_mate_equipment_used)
	
	# Radio Operator signals
	radio_operator_ai.enemy_position_updated.connect(_on_radio_enemy_position_updated)
	radio_operator_ai.intelligence_gathered.connect(_on_radio_intelligence_gathered)
	
	if debug_mode:
		print("AIController: Station signals connected")

## Public API

func start_ai_turn() -> void:
	## Start a new AI turn with all stations processing in parallel
	
	if not is_initialized:
		push_error("AIController: Cannot start turn - not initialized")
		return
	
	current_turn += 1
	
	if debug_mode:
		print("AIController: Starting turn ", current_turn)
	
	# Notify event bus of turn start
	ai_event_bus.emit_event("turn_started", {"turn": current_turn})

	# Process hard mode systems if enabled
	var hard_mode_decisions = {}
	if hard_mode_manager:
		var game_state = _get_current_game_state()
		hard_mode_decisions = hard_mode_manager.process_turn(game_state, ai_personality)

		if debug_mode and not hard_mode_decisions.is_empty():
			print("AIController: Hard mode decisions generated")

	# Start all stations processing
	captain_ai.start_turn()
	engineer_ai.start_turn()
	first_mate_ai.start_turn()
	radio_operator_ai.start_turn()
	
	# Monitor for turn completion
	_monitor_turn_completion()

func set_difficulty_profile(profile: AIPersonality) -> void:
	## Update the AI personality/difficulty profile
	
	ai_personality = profile
	
	# Update all stations with new profile
	if captain_ai:
		captain_ai.update_personality(ai_personality)
	if engineer_ai:
		engineer_ai.update_personality(ai_personality)
	if first_mate_ai:
		first_mate_ai.update_personality(ai_personality)
	if radio_operator_ai:
		radio_operator_ai.update_personality(ai_personality)
	
	if debug_mode:
		print("AIController: Difficulty profile updated")

func update_game_state(position: Vector2i, health: int, surfaced: bool) -> void:
	## Update the current game state for AI decision making
	
	submarine_position = position
	submarine_health = health
	is_surfaced = surfaced
	
	# Broadcast state update to all stations via event bus
	ai_event_bus.emit_event("game_state_updated", {
		"position": position,
		"health": health,
		"surfaced": surfaced,
		"turn": current_turn
	})

# Overloaded version for compatibility with Dictionary parameter
func update_game_state_dict(state_dict: Dictionary) -> void:
	## Update game state using a dictionary (for compatibility)

	var position = state_dict.get("position", submarine_position)
	var health = state_dict.get("health", submarine_health)
	var surfaced = state_dict.get("surfaced", is_surfaced)

	# Store additional state data in a global game_state dictionary
	if not has_meta("game_state"):
		set_meta("game_state", {})

	var game_state_meta = get_meta("game_state")
	for key in state_dict:
		game_state_meta[key] = state_dict[key]

	# Call the main update method
	update_game_state(position, health, surfaced)

# Property to access game state for compatibility
var game_state: Dictionary:
	get:
		if has_meta("game_state"):
			return get_meta("game_state")
		return {}
	set(value):
		set_meta("game_state", value)

func get_strategic_recommendation(station_name: String, context: Dictionary = {}) -> Dictionary:
	## Get strategic recommendation for a station from hard mode systems

	if not hard_mode_manager:
		return {}

	return hard_mode_manager.get_strategic_recommendation(station_name, context)

func predict_enemy_behavior(turns_ahead: int = 3) -> Array[Dictionary]:
	## Predict enemy behavior using hard mode AI systems

	if not hard_mode_manager:
		return []

	var current_state = _get_current_game_state()
	return hard_mode_manager.predict_enemy_behavior(current_state, turns_ahead)

func get_hard_mode_status() -> Dictionary:
	## Get status of hard mode AI systems

	if not hard_mode_manager:
		return {"active": false}

	return hard_mode_manager.get_hard_mode_status()

func get_ai_status() -> Dictionary:
	## Get comprehensive status of all AI systems
	
	var status = {
		"initialized": is_initialized,
		"current_turn": current_turn,
		"team_id": team_id,
		"submarine_position": submarine_position,
		"submarine_health": submarine_health,
		"is_surfaced": is_surfaced,
		"stations": {
			"captain": captain_ai.get_status() if captain_ai else {},
			"engineer": engineer_ai.get_status() if engineer_ai else {},
			"first_mate": first_mate_ai.get_status() if first_mate_ai else {},
			"radio_operator": radio_operator_ai.get_status() if radio_operator_ai else {}
		}
	}

	# Add hard mode status if available
	if hard_mode_manager:
		status["hard_mode"] = hard_mode_manager.get_hard_mode_status()

	return status

## Private methods

func _monitor_turn_completion() -> void:
	## Monitor all stations for turn completion
	
	# Use a timer to periodically check completion status
	var completion_timer = get_tree().create_timer(0.1, false)
	completion_timer.timeout.connect(_check_turn_completion)

func _check_turn_completion() -> void:
	## Check if all stations have completed their turn
	
	var all_complete = true
	
	if captain_ai and not captain_ai.is_turn_complete():
		all_complete = false
	if engineer_ai and not engineer_ai.is_turn_complete():
		all_complete = false
	if first_mate_ai and not first_mate_ai.is_turn_complete():
		all_complete = false
	if radio_operator_ai and not radio_operator_ai.is_turn_complete():
		all_complete = false
	
	if all_complete:
		_on_turn_completed()
	else:
		# Continue monitoring
		_monitor_turn_completion()

func _on_turn_completed() -> void:
	## Handle turn completion
	
	if debug_mode:
		print("AIController: Turn ", current_turn, " completed")
	
	# Notify event bus
	ai_event_bus.emit_event("turn_completed", {"turn": current_turn})
	
	# Emit signal for external systems
	emit_signal("ai_turn_completed")

## Station signal handlers

func _on_captain_movement_decision(direction: String) -> void:
	## Handle captain's movement decision
	
	if debug_mode:
		print("AIController: Captain decided to move ", direction)
	
	emit_signal("ai_movement_decision", direction)
	emit_signal("ai_action_completed", "captain", "movement")

func _on_captain_combat_action(action: String, target: Vector2i) -> void:
	## Handle captain's combat action
	
	if debug_mode:
		print("AIController: Captain taking combat action: ", action, " at ", target)
	
	emit_signal("ai_action_completed", "captain", action)

func _on_engineer_surface_recommendation(should_surface: bool) -> void:
	## Handle engineer's surface recommendation
	
	if should_surface:
		if debug_mode:
			print("AIController: Engineer recommends surfacing")
		emit_signal("ai_wants_to_surface")
	
	emit_signal("ai_action_completed", "engineer", "surface_check")

func _on_engineer_system_repaired(system_name: String) -> void:
	## Handle engineer system repair
	
	if debug_mode:
		print("AIController: Engineer repaired system: ", system_name)
	
	emit_signal("ai_action_completed", "engineer", "repair")

func _on_first_mate_equipment_ready(equipment_name: String) -> void:
	## Handle first mate equipment ready notification
	
	if debug_mode:
		print("AIController: First Mate reports ", equipment_name, " ready")
	
	emit_signal("ai_action_completed", "first_mate", "charge")

func _on_first_mate_equipment_used(equipment_name: String) -> void:
	## Handle first mate equipment usage
	
	if debug_mode:
		print("AIController: First Mate used ", equipment_name)
	
	emit_signal("ai_action_completed", "first_mate", "use_equipment")

func _on_radio_enemy_position_updated(position: Vector2i, certainty: float) -> void:
	## Handle radio operator enemy position update
	
	if debug_mode:
		print("AIController: Radio Operator estimates enemy at ", position, " (certainty: ", certainty, ")")
	
	# Share intelligence with other stations via event bus
	ai_event_bus.emit_event("enemy_position_updated", {
		"position": position,
		"certainty": certainty,
		"source": "radio_operator"
	})
	
	emit_signal("ai_action_completed", "radio_operator", "track_enemy")

func _on_radio_intelligence_gathered(intelligence: Dictionary) -> void:
	## Handle radio operator intelligence gathering
	
	if debug_mode:
		print("AIController: Radio Operator gathered intelligence: ", intelligence)
	
	# Broadcast intelligence to all stations
	ai_event_bus.emit_event("intelligence_gathered", intelligence)
	
	emit_signal("ai_action_completed", "radio_operator", "gather_intelligence")

## Private helper methods

func _get_current_game_state() -> Dictionary:
	## Get current game state for hard mode processing

	return {
		"turn_count": current_turn,
		"our_position": submarine_position,
		"our_health": submarine_health,
		"enemy_position": Vector2i.ZERO,  # Would be updated by radio operator
		"enemy_health": 4,  # Would be tracked by game system
		"enemy_detected": false,  # Would be updated by detection systems
		"is_surfaced": is_surfaced,
		"systems_status": _get_systems_status(),
		"available_weapons": _get_available_weapons(),
		"timestamp": Time.get_unix_time_from_system()
	}

func _get_systems_status() -> Dictionary:
	## Get current systems status

	# This would be populated by the engineer station
	return {
		"engine": {"health": 4, "max_health": 4},
		"weapons": {"health": 4, "max_health": 4},
		"sonar": {"health": 4, "max_health": 4},
		"reactor": {"health": 4, "max_health": 4}
	}

func _get_available_weapons() -> Array:
	## Get currently available weapons

	# This would be populated by the first mate station
	return ["torpedo", "mine", "drone", "sonar"]
