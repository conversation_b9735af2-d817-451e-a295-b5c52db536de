extends ActionNode
class_name CoordinateWithStationsAction

## Coordinate With Stations Action
##
## Action to coordinate with other stations

func _init() -> void:
	super("CoordinateWithStations")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("CoordinateWithStationsAction: Coordinating with other stations")
	
	ctx.emit_event("station_coordination", {
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
