extends Node
class_name AIBehaviorManager

## AI Behavior Manager - Coordinates Behavior Trees
##
## This class manages the execution of behavior trees across all AI stations,
## providing coordination, performance monitoring, and shared context management.
## It replaces the complex decision logic coordination in the old system.

## References to core systems
var ai_personality: AIPersonality
var ai_event_bus: AIEventBus

## Behavior tree registry - tracks all active behavior trees
var registered_trees: Dictionary = {}  # station_name -> BehaviorNode
var tree_contexts: Dictionary = {}     # station_name -> BehaviorContext
var tree_performance: Dictionary = {}  # station_name -> performance stats

## Shared blackboard for inter-station communication
var global_blackboard: Dictionary = {}

## Execution settings
var max_execution_time_per_frame: float = 0.001  # 1ms per frame
var debug_mode: bool = false

## Performance monitoring
var total_executions: int = 0
var total_execution_time: float = 0.0
var frame_execution_count: int = 0

func setup(personality: AIPersonality, event_bus: AIEventBus) -> void:
	## Initialize the behavior manager with core systems
	
	ai_personality = personality
	ai_event_bus = event_bus
	
	# Subscribe to relevant events
	if ai_event_bus:
		ai_event_bus.subscribe(self, "game_state_updated", _on_game_state_updated)
		ai_event_bus.subscribe(self, "turn_started", _on_turn_started)
		ai_event_bus.subscribe(self, "turn_completed", _on_turn_completed)
	
	if debug_mode:
		print("AIBehaviorManager: Initialized with personality and event bus")

## Public API

func register_behavior_tree(station_name: String, root_node: BehaviorNode, owner: Node) -> void:
	## Register a behavior tree for a specific station
	
	registered_trees[station_name] = root_node
	
	# Create context for this tree
	var context = BehaviorContext.new(get_ai_controller(), owner)
	if ai_personality:
		context.ai_personality = ai_personality
	if ai_event_bus:
		context.ai_event_bus = ai_event_bus
	
	tree_contexts[station_name] = context
	
	# Initialize performance tracking
	tree_performance[station_name] = {
		"executions": 0,
		"total_time": 0.0,
		"average_time": 0.0,
		"last_result": BehaviorNode.Status.FAILURE,
		"last_execution_time": 0.0
	}
	
	if debug_mode:
		print("AIBehaviorManager: Registered behavior tree for ", station_name)

func unregister_behavior_tree(station_name: String) -> void:
	## Unregister a behavior tree
	
	registered_trees.erase(station_name)
	tree_contexts.erase(station_name)
	tree_performance.erase(station_name)
	
	if debug_mode:
		print("AIBehaviorManager: Unregistered behavior tree for ", station_name)

func execute_behavior_tree(station_name: String) -> BehaviorNode.Status:
	## Execute a specific station's behavior tree
	
	if not registered_trees.has(station_name):
		push_warning("AIBehaviorManager: No behavior tree registered for " + station_name)
		return BehaviorNode.Status.FAILURE
	
	var tree = registered_trees[station_name]
	var context = tree_contexts[station_name]
	
	if not tree or not context:
		return BehaviorNode.Status.FAILURE
	
	# Update context with current game state
	_update_context(context)
	
	# Execute with performance monitoring
	var start_time = Time.get_unix_time_from_system()
	var result = tree.execute(context)
	var execution_time = Time.get_unix_time_from_system() - start_time
	
	# Update performance stats
	_update_performance_stats(station_name, result, execution_time)
	
	if debug_mode:
		print("AIBehaviorManager: ", station_name, " behavior tree executed with result: ", 
			  _status_to_string(result), " in ", "%.3f" % (execution_time * 1000), "ms")
	
	return result

func execute_all_trees() -> Dictionary:
	## Execute all registered behavior trees
	
	var results = {}
	var total_frame_time = 0.0
	frame_execution_count = 0
	
	for station_name in registered_trees:
		var start_time = Time.get_unix_time_from_system()
		
		results[station_name] = execute_behavior_tree(station_name)
		
		var execution_time = Time.get_unix_time_from_system() - start_time
		total_frame_time += execution_time
		frame_execution_count += 1
		
		# Check frame time budget
		if total_frame_time > max_execution_time_per_frame:
			if debug_mode:
				print("AIBehaviorManager: Frame time budget exceeded, deferring remaining trees")
			break
	
	return results

func update_personality(new_personality: AIPersonality) -> void:
	## Update the personality for all behavior trees
	
	ai_personality = new_personality
	
	# Update all contexts
	for context in tree_contexts.values():
		context.ai_personality = ai_personality
	
	if debug_mode:
		print("AIBehaviorManager: Updated personality for all behavior trees")

func get_global_blackboard_value(key: String, default_value = null):
	## Get a value from the global blackboard
	return global_blackboard.get(key, default_value)

func set_global_blackboard_value(key: String, value) -> void:
	## Set a value in the global blackboard
	global_blackboard[key] = value
	
	# Notify all contexts of the change
	for context in tree_contexts.values():
		context.set_blackboard_value("global_" + key, value)

func clear_global_blackboard() -> void:
	## Clear the global blackboard
	global_blackboard.clear()
	
	# Clear global values from all contexts
	for context in tree_contexts.values():
		var keys_to_remove = []
		for key in context.blackboard:
			if key.begins_with("global_"):
				keys_to_remove.append(key)
		
		for key in keys_to_remove:
			context.blackboard.erase(key)

func get_performance_stats() -> Dictionary:
	## Get comprehensive performance statistics
	
	var stats = {
		"total_executions": total_executions,
		"total_execution_time": total_execution_time,
		"average_execution_time": (total_execution_time / total_executions) if total_executions > 0 else 0.0,
		"frame_execution_count": frame_execution_count,
		"registered_trees": registered_trees.size(),
		"station_stats": tree_performance.duplicate()
	}
	
	return stats

func reset_all_trees() -> void:
	## Reset all registered behavior trees
	
	for tree in registered_trees.values():
		tree.reset()
	
	# Clear all blackboards
	clear_global_blackboard()
	for context in tree_contexts.values():
		context.clear_blackboard()
	
	if debug_mode:
		print("AIBehaviorManager: Reset all behavior trees")

## Private methods

func get_ai_controller() -> AIController:
	## Get reference to the AI controller
	var controller_group = get_tree().get_nodes_in_group("ai_controller")
	return controller_group[0] if not controller_group.is_empty() else null

func _update_context(context: BehaviorContext) -> void:
	## Update a behavior context with current game state
	
	var ai_controller = get_ai_controller()
	if not ai_controller:
		return
	
	# Update game state
	context.update_game_state(
		ai_controller.submarine_position,
		ai_controller.submarine_health,
		ai_controller.is_surfaced,
		ai_controller.current_turn
	)
	
	# Update global blackboard values
	for key in global_blackboard:
		context.set_blackboard_value("global_" + key, global_blackboard[key])

func _update_performance_stats(station_name: String, result: BehaviorNode.Status, execution_time: float) -> void:
	## Update performance statistics for a station
	
	if not tree_performance.has(station_name):
		return
	
	var stats = tree_performance[station_name]
	stats.executions += 1
	stats.total_time += execution_time
	stats.average_time = stats.total_time / stats.executions
	stats.last_result = result
	stats.last_execution_time = execution_time
	
	# Update global stats
	total_executions += 1
	total_execution_time += execution_time

func _status_to_string(status: BehaviorNode.Status) -> String:
	## Convert status enum to string for debugging
	
	match status:
		BehaviorNode.Status.SUCCESS:
			return "SUCCESS"
		BehaviorNode.Status.FAILURE:
			return "FAILURE"
		BehaviorNode.Status.RUNNING:
			return "RUNNING"
		_:
			return "UNKNOWN"

## Event handlers

func _on_game_state_updated(event: Dictionary) -> void:
	## Handle game state updates
	
	var data = event.data
	
	# Update global blackboard with game state
	set_global_blackboard_value("submarine_position", data.get("position", Vector2i.ZERO))
	set_global_blackboard_value("submarine_health", data.get("health", 4))
	set_global_blackboard_value("is_surfaced", data.get("surfaced", false))
	set_global_blackboard_value("current_turn", data.get("turn", 0))

func _on_turn_started(event: Dictionary) -> void:
	## Handle turn start
	
	var turn_number = event.data.get("turn", 0)
	set_global_blackboard_value("current_turn", turn_number)
	
	if debug_mode:
		print("AIBehaviorManager: Turn ", turn_number, " started")

func _on_turn_completed(event: Dictionary) -> void:
	## Handle turn completion
	
	# Reset frame execution count
	frame_execution_count = 0
	
	if debug_mode:
		var turn_number = event.data.get("turn", 0)
		print("AIBehaviorManager: Turn ", turn_number, " completed")

## Debug and monitoring methods

func enable_debug_mode(enable: bool = true) -> void:
	## Enable or disable debug mode
	debug_mode = enable
	
	# Enable debug for all registered trees
	for tree in registered_trees.values():
		tree.enable_debug(enable)

func print_performance_stats() -> void:
	## Print current performance statistics
	var stats = get_performance_stats()
	
	print("=== AI Behavior Manager Performance ===")
	print("Total executions: ", stats.total_executions)
	print("Total execution time: ", "%.3f" % (stats.total_execution_time * 1000), "ms")
	print("Average execution time: ", "%.3f" % (stats.average_execution_time * 1000), "ms")
	print("Registered trees: ", stats.registered_trees)
	print("Frame execution count: ", stats.frame_execution_count)
	
	print("\nStation Statistics:")
	for station_name in stats.station_stats:
		var station_stats = stats.station_stats[station_name]
		print("  ", station_name, ":")
		print("    Executions: ", station_stats.executions)
		print("    Average time: ", "%.3f" % (station_stats.average_time * 1000), "ms")
		print("    Last result: ", _status_to_string(station_stats.last_result))

func validate_all_trees() -> Dictionary:
	## Validate all registered behavior trees
	var validation_results = {}
	
	for station_name in registered_trees:
		var tree = registered_trees[station_name]
		var context = tree_contexts[station_name]
		
		validation_results[station_name] = {
			"tree_valid": tree != null,
			"context_valid": context != null,
			"tree_type": tree.get_script().get_global_name() if tree else "null",
			"performance_tracked": tree_performance.has(station_name)
		}
	
	return validation_results

func get_tree_status(station_name: String) -> Dictionary:
	## Get detailed status of a specific behavior tree
	
	if not registered_trees.has(station_name):
		return {"error": "Tree not found"}
	
	var tree = registered_trees[station_name]
	var context = tree_contexts[station_name]
	var performance = tree_performance.get(station_name, {})
	
	return {
		"station_name": station_name,
		"tree_registered": true,
		"tree_status": tree.get_status_string() if tree else "null",
		"context_blackboard_size": context.blackboard.size() if context else 0,
		"performance": performance,
		"global_blackboard_size": global_blackboard.size()
	}
