extends AIStation
class_name AIRadioOperator

## AI Radio Operator Station - Intelligence and Enemy Tracking
##
## The Radio Operator AI is responsible for:
## - Enemy position tracking and prediction
## - Intelligence analysis and reporting
## - Communication coordination between stations
## - Pattern recognition and learning from enemy behavior
##
## Uses behavior tree-based decision making with sophisticated tracking algorithms.

signal enemy_position_updated(estimated_position: Vector2i, certainty: float)
signal enemy_surfaced()
signal intelligence_gathered(info_type: String, data: Dictionary)

## RadioOperator-specific state
var enemy_track_history: Array[Dictionary] = []
var intelligence_reports: Array[Dictionary] = []
var tracking_confidence: float = 0.0
var last_enemy_contact: float = 0.0
var prediction_accuracy: float = 0.5

## RadioOperator parameters (from personality)
var tracking_skill: float = 0.5
var analysis_depth: float = 0.5
var communication_efficiency: float = 0.5
var pattern_recognition: float = 0.5

func _init() -> void:
	super()
	station_name = "radiooperator"
	station_type = "radiooperator"

func create_behavior_tree() -> BehaviorNode:
	## Create the Radio Operator's behavior tree
	## Focuses on intelligence gathering and enemy tracking

	# Root selector - choose between different operational modes
	var root = SelectorNode.new("RadioOperatorRoot")

	# Active tracking (when enemy is detected)
	var active_tracking_sequence = SequenceNode.new("ActiveTracking")
	active_tracking_sequence.add_child(EnemyDetectedCondition.new(0.1))  # Any detection
	active_tracking_sequence.add_child(_create_active_tracking_behavior())

	# Intelligence analysis (when we have data but no current contact)
	var analysis_behavior = _create_intelligence_analysis_behavior()

	# Passive monitoring (default behavior)
	var passive_monitoring = _create_passive_monitoring_behavior()

	# Communication support
	var communication_support = _create_communication_support_behavior()

	# Idle behavior (lowest priority)
	var idle_behavior = _create_idle_behavior()

	# Add all behaviors to root selector
	root.add_child(active_tracking_sequence)
	root.add_child(analysis_behavior)
	root.add_child(passive_monitoring)
	root.add_child(communication_support)
	root.add_child(idle_behavior)

	return root

func _create_active_tracking_behavior() -> BehaviorNode:
	## Create active enemy tracking behavior tree

	var tracking_parallel = ParallelNode.new("ActiveTrackingParallel", [],
		ParallelNode.Policy.REQUIRE_ONE, ParallelNode.Policy.REQUIRE_ALL)

	# Track current position
	var track_sequence = SequenceNode.new("TrackPosition")
	track_sequence.add_child(TrackEnemyAction.new())
	track_sequence.add_child(UpdateTrackingDataAction.new())

	# Predict next position
	var predict_sequence = SequenceNode.new("PredictPosition")
	predict_sequence.add_child(PredictEnemyMovementAction.new())
	predict_sequence.add_child(UpdatePredictionAction.new())

	# Report findings
	var report_sequence = SequenceNode.new("ReportFindings")
	report_sequence.add_child(CooldownNode.new("ReportCooldown", GenerateIntelligenceReportAction.new(), 2.0))

	tracking_parallel.add_child(track_sequence)
	tracking_parallel.add_child(predict_sequence)
	tracking_parallel.add_child(report_sequence)

	return tracking_parallel

func _create_intelligence_analysis_behavior() -> BehaviorNode:
	## Create intelligence analysis behavior tree

	var analysis_sequence = SequenceNode.new("IntelligenceAnalysis")

	# Only analyze if we have historical data
	var has_data_condition = HasTrackingDataCondition.new()
	analysis_sequence.add_child(has_data_condition)

	# Analysis actions
	var analysis_selector = WeightedSelectorNode.new("AnalysisActions")

	# Pattern analysis (high weight for skilled operators)
	var pattern_analysis = PatternAnalysisAction.new()
	analysis_selector.add_child_with_weight(pattern_analysis, pattern_recognition)

	# Historical analysis
	var historical_analysis = HistoricalAnalysisAction.new()
	analysis_selector.add_child_with_weight(historical_analysis, analysis_depth)

	# Trend analysis
	var trend_analysis = TrendAnalysisAction.new()
	analysis_selector.add_child_with_weight(trend_analysis, 0.3)

	analysis_sequence.add_child(analysis_selector)

	return analysis_sequence

func _create_passive_monitoring_behavior() -> BehaviorNode:
	## Create passive monitoring behavior tree

	var monitoring_selector = WeightedSelectorNode.new("PassiveMonitoring")

	# Listen for enemy activity
	var listen_action = ListenForEnemyAction.new()
	monitoring_selector.add_child_with_weight(listen_action, 0.4)

	# Scan for signals
	var scan_action = ScanForSignalsAction.new()
	monitoring_selector.add_child_with_weight(scan_action, 0.3)

	# Monitor communications
	var monitor_action = MonitorCommunicationsAction.new()
	monitoring_selector.add_child_with_weight(monitor_action, 0.3)

	return monitoring_selector

func _create_communication_support_behavior() -> BehaviorNode:
	## Create communication support behavior tree

	var comm_selector = SelectorNode.new("CommunicationSupport")

	# Handle communication requests
	var request_sequence = SequenceNode.new("HandleRequests")
	request_sequence.add_child(HasCommunicationRequestCondition.new())
	request_sequence.add_child(ProcessCommunicationRequestAction.new())

	# Coordinate with other stations
	var coordinate_action = CoordinateWithStationsAction.new()

	comm_selector.add_child(request_sequence)
	comm_selector.add_child(coordinate_action)

	return comm_selector

func _create_idle_behavior() -> BehaviorNode:
	## Create idle behavior for when no other actions are needed

	return IdleAction.new()

## Station-specific overrides

func _initialize_station() -> void:
	## Initialize RadioOperator-specific systems

	# Initialize tracking systems
	enemy_track_history.clear()
	intelligence_reports.clear()
	tracking_confidence = 0.0
	last_enemy_contact = 0.0
	prediction_accuracy = 0.5

	if debug_mode:
		print("AIRadioOperator: Radio Operator station initialized")

func _on_personality_updated(new_personality: AIPersonality) -> void:
	## Handle personality parameter updates

	var radio_params = new_personality.get_station_parameters("radio_operator")

	tracking_skill = radio_params.get("tracking_skill", 0.5)
	analysis_depth = radio_params.get("analysis_depth", 0.5)
	communication_efficiency = radio_params.get("communication_efficiency", 0.5)
	pattern_recognition = radio_params.get("pattern_recognition", 0.5)

	if debug_mode:
		print("AIRadioOperator: Updated personality - Tracking: ", tracking_skill,
			  ", Analysis: ", analysis_depth, ", Communication: ", communication_efficiency,
			  ", Pattern Recognition: ", pattern_recognition)

func _subscribe_to_events() -> void:
	## Subscribe to RadioOperator-relevant events

	super._subscribe_to_events()

	if ai_event_bus:
		ai_event_bus.subscribe(self, "enemy_detected", _on_enemy_detected)
		ai_event_bus.subscribe(self, "enemy_moved", _on_enemy_moved)
		ai_event_bus.subscribe(self, "sonar_contact", _on_sonar_contact)
		ai_event_bus.subscribe(self, "communication_request", _on_communication_request)
		ai_event_bus.subscribe(self, "intelligence_request", _on_intelligence_request)

func _on_enemy_detected(event: Dictionary) -> void:
	## Handle enemy detection events

	var detection_data = event.data
	var position = detection_data.get("position", Vector2i.ZERO)
	var certainty = detection_data.get("certainty", 0.0)
	var timestamp = Time.get_unix_time_from_system()

	# Add to tracking history
	var track_entry = {
		"position": position,
		"certainty": certainty,
		"timestamp": timestamp,
		"source": "detection"
	}

	enemy_track_history.append(track_entry)
	_update_tracking_confidence()
	last_enemy_contact = timestamp

	# Emit signal for legacy compatibility
	enemy_position_updated.emit(position, certainty)

	if debug_mode:
		print("AIRadioOperator: Enemy detected at ", position, " with certainty ", certainty)

func _on_enemy_moved(event: Dictionary) -> void:
	## Handle enemy movement events

	var movement_data = event.data
	var new_position = movement_data.get("position", Vector2i.ZERO)
	var timestamp = Time.get_unix_time_from_system()

	# Add to tracking history
	var track_entry = {
		"position": new_position,
		"certainty": 0.8,  # Movement tracking is fairly reliable
		"timestamp": timestamp,
		"source": "movement"
	}

	enemy_track_history.append(track_entry)
	_update_tracking_confidence()
	_update_prediction_accuracy()
	last_enemy_contact = timestamp

	# Emit signal for legacy compatibility
	enemy_position_updated.emit(new_position, 0.8)

	if debug_mode:
		print("AIRadioOperator: Enemy moved to ", new_position)

func _on_sonar_contact(event: Dictionary) -> void:
	## Handle sonar contact events

	var sonar_data = event.data
	var position = sonar_data.get("position", Vector2i.ZERO)
	var strength = sonar_data.get("strength", 0.5)
	var timestamp = Time.get_unix_time_from_system()

	# Add to tracking history
	var track_entry = {
		"position": position,
		"certainty": strength,
		"timestamp": timestamp,
		"source": "sonar"
	}

	enemy_track_history.append(track_entry)
	_update_tracking_confidence()
	last_enemy_contact = timestamp

	# Emit signal for legacy compatibility
	enemy_position_updated.emit(position, strength)

	if debug_mode:
		print("AIRadioOperator: Sonar contact at ", position, " strength ", strength)

func _on_communication_request(event: Dictionary) -> void:
	## Handle communication requests

	var request_data = event.data
	var request_type = request_data.get("type", "")
	var requester = request_data.get("station", "")

	_process_communication_request(request_type, requester)

	if debug_mode:
		print("AIRadioOperator: Communication request from ", requester, ": ", request_type)

func _on_intelligence_request(event: Dictionary) -> void:
	## Handle intelligence requests

	var request_data = event.data
	var request_type = request_data.get("type", "")
	var requester = request_data.get("station", "")

	var intelligence_data = _generate_intelligence_report(request_type)

	# Emit signal for legacy compatibility
	intelligence_gathered.emit(request_type, intelligence_data)

	if debug_mode:
		print("AIRadioOperator: Intelligence request from ", requester, ": ", request_type)

## Helper methods

func _update_tracking_confidence() -> void:
	## Update tracking confidence based on recent data

	var recent_tracks = _get_recent_tracks(60.0)  # Last minute
	if recent_tracks.is_empty():
		tracking_confidence = 0.0
		return

	var total_certainty = 0.0
	for track in recent_tracks:
		total_certainty += track.certainty

	tracking_confidence = total_certainty / float(recent_tracks.size())

func _update_prediction_accuracy() -> void:
	## Update prediction accuracy based on validation

	# This would be updated when predictions are validated
	# For now, maintain current accuracy with slight drift
	prediction_accuracy = clamp(prediction_accuracy + randf_range(-0.05, 0.05), 0.0, 1.0)

func _get_recent_tracks(time_window: float) -> Array[Dictionary]:
	## Get tracking data within time window

	var recent_tracks: Array[Dictionary] = []
	var current_time = Time.get_unix_time_from_system()

	for track in enemy_track_history:
		if current_time - track.timestamp <= time_window:
			recent_tracks.append(track)

	return recent_tracks

func _process_communication_request(request_type: String, requester: String) -> void:
	## Process communication request

	match request_type:
		"enemy_status":
			_send_enemy_status(requester)
		"enemy_prediction":
			_send_enemy_prediction(requester)
		"pattern_analysis":
			_send_pattern_analysis(requester)
		"threat_assessment":
			_send_threat_assessment(requester)

func _generate_intelligence_report(report_type: String) -> Dictionary:
	## Generate intelligence report

	var report = {
		"type": report_type,
		"timestamp": Time.get_unix_time_from_system(),
		"confidence": tracking_confidence
	}

	match report_type:
		"enemy_position":
			if not enemy_track_history.is_empty():
				report["position"] = enemy_track_history[-1].position
				report["certainty"] = enemy_track_history[-1].certainty
		"movement_patterns":
			report["patterns"] = _analyze_movement_patterns()
		"threat_level":
			report["threat_level"] = _calculate_threat_level()
			report["description"] = _get_threat_description(report.threat_level)

	return report

func _send_enemy_status(requester: String) -> void:
	## Send enemy status information

	if ai_event_bus and not enemy_track_history.is_empty():
		var last_track = enemy_track_history[-1]
		ai_event_bus.emit_event("enemy_status", {
			"requester": requester,
			"position": last_track.position,
			"certainty": last_track.certainty,
			"last_contact": last_enemy_contact,
			"tracking_confidence": tracking_confidence,
			"prediction_accuracy": prediction_accuracy,
			"source": "radiooperator"
		})

func _send_enemy_prediction(requester: String) -> void:
	## Send enemy movement prediction

	if ai_event_bus and not enemy_track_history.is_empty():
		var predicted_position = _calculate_predicted_position()
		ai_event_bus.emit_event("enemy_prediction", {
			"requester": requester,
			"predicted_position": predicted_position,
			"accuracy": prediction_accuracy,
			"timestamp": Time.get_unix_time_from_system(),
			"source": "radiooperator"
		})

func _send_pattern_analysis(requester: String) -> void:
	## Send pattern analysis

	if ai_event_bus:
		var patterns = _analyze_movement_patterns()
		ai_event_bus.emit_event("pattern_analysis", {
			"requester": requester,
			"patterns": patterns,
			"confidence": pattern_recognition,
			"source": "radiooperator"
		})

func _send_threat_assessment(requester: String) -> void:
	## Send threat assessment

	if ai_event_bus:
		var threat_level = _calculate_threat_level()
		ai_event_bus.emit_event("threat_assessment", {
			"requester": requester,
			"threat_level": threat_level,
			"assessment": _get_threat_description(threat_level),
			"source": "radiooperator"
		})

func _calculate_predicted_position() -> Vector2i:
	## Calculate predicted enemy position

	if enemy_track_history.size() < 2:
		return enemy_track_history[-1].position if not enemy_track_history.is_empty() else Vector2i.ZERO

	# Simple linear prediction based on last two positions
	var last_pos = enemy_track_history[-1].position
	var prev_pos = enemy_track_history[-2].position
	var movement = last_pos - prev_pos

	return last_pos + movement

func _analyze_movement_patterns() -> Array[String]:
	## Analyze enemy movement patterns

	var patterns: Array[String] = []

	if enemy_track_history.size() < 3:
		return patterns

	# Simple pattern detection
	var recent_tracks = _get_recent_tracks(60.0)  # Last minute

	if recent_tracks.size() >= 3:
		patterns.append("linear_movement")

	if tracking_confidence > 0.7:
		patterns.append("predictable_behavior")

	return patterns

func _calculate_threat_level() -> float:
	## Calculate current threat level

	var threat = 0.0

	# Base threat on tracking confidence
	threat += tracking_confidence * 0.4

	# Increase threat if enemy is close
	if not enemy_track_history.is_empty():
		var last_position = enemy_track_history[-1].position
		# This would need actual submarine position from context
		threat += 0.3

	# Recent activity increases threat
	var recent_activity = _get_recent_tracks(30.0).size()
	threat += min(recent_activity * 0.1, 0.3)

	return min(threat, 1.0)

func _get_threat_description(threat_level: float) -> String:
	## Get threat level description

	if threat_level < 0.3:
		return "low"
	elif threat_level < 0.7:
		return "medium"
	else:
		return "high"

func get_radiooperator_status() -> Dictionary:
	## Get RadioOperator-specific status information

	var base_status = get_status()
	base_status.merge({
		"enemy_track_history_size": enemy_track_history.size(),
		"intelligence_reports_count": intelligence_reports.size(),
		"tracking_confidence": tracking_confidence,
		"last_enemy_contact": last_enemy_contact,
		"prediction_accuracy": prediction_accuracy,
		"tracking_skill": tracking_skill,
		"analysis_depth": analysis_depth,
		"communication_efficiency": communication_efficiency,
		"pattern_recognition": pattern_recognition
	})

	return base_status

## Legacy compatibility methods for backward compatibility

func setup(personality: AIPersonality, event_bus: AIEventBus, behavior_manager: AIBehaviorManager) -> void:
	## Override parent setup method
	super.setup(personality, event_bus, behavior_manager)

func is_done() -> bool:
	## Legacy method for backward compatibility
	return get_turn_complete_status()

func set_game_over(is_over: bool) -> void:
	## Legacy method for backward compatibility
	pass

func start_turn() -> void:
	## Legacy method for backward compatibility
	super.start_turn()

func process_station() -> void:
	## Legacy method for backward compatibility
	start_turn()

func get_debug_info() -> Dictionary:
	## Legacy method for backward compatibility
	return get_radiooperator_status()

func get_enemy_position() -> Vector2i:
	## Legacy method for backward compatibility
	if not enemy_track_history.is_empty():
		return enemy_track_history[-1].position
	return Vector2i.ZERO

func get_enemy_certainty() -> float:
	## Legacy method for backward compatibility
	return tracking_confidence

func update_enemy_info(pos: Vector2i, certainty: float) -> void:
	## Legacy method for backward compatibility
	var track_entry = {
		"position": pos,
		"certainty": certainty,
		"timestamp": Time.get_unix_time_from_system(),
		"source": "legacy"
	}
	enemy_track_history.append(track_entry)
	_update_tracking_confidence()

func get_tracking_data() -> Array[Dictionary]:
	## Legacy method for backward compatibility
	return enemy_track_history

func get_intelligence_reports() -> Array[Dictionary]:
	## Legacy method for backward compatibility
	return intelligence_reports
