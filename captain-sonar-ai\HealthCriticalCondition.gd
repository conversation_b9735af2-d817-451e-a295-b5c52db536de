class_name HealthCriticalCondition extends ConditionNode

## Checks if submarine health is critically low

func _init(name: String = "HealthCritical") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Check if health is critical
	if ctx.is_health_critical():
		return Status.SUCCESS
	else:
		return Status.FAILURE
## Checks if submarine health is critically low (1 or less)

func _init() -> void:
	super("HealthCritical")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	return Status.SUCCESS if ctx.is_health_critical() else Status.FAILURE
