class_name ChargeEquipmentAction extends ActionNode

## Action to charge equipment

var equipment_name: String = ""

func _init(name: String = "ChargeEquipment", equipment: String = "") -> void:
	super(name)
	equipment_name = equipment

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute equipment charging

	# Prepare charge data
	var charge_data = {
		"equipment": equipment_name,
		"priority": "normal"
	}

	# Emit charge command event
	ctx.emit_event("charge_equipment_command", charge_data)

	return Status.SUCCESS
## Action to charge specific equipment

var equipment_name: String = ""

func _init(equipment: String) -> void:
	super("ChargeEquipment_" + equipment)
	equipment_name = equipment

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if ctx.can_use_equipment(equipment_name):
		return Status.SUCCESS  # Already charged
	
	if debug_enabled:
		print("ChargeEquipmentAction: Charging ", equipment_name)
	
	ctx.emit_event("equipment_charge", {"equipment": equipment_name})
	return Status.SUCCESS
