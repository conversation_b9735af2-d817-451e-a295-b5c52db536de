extends ActionNode
class_name ChargeEquipmentAction

## Charge Equipment Action
##
## Action to charge specific equipment

var equipment_name: String = ""

func _init(equipment: String) -> void:
	super("ChargeEquipment_" + equipment)
	equipment_name = equipment

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if ctx.can_use_equipment(equipment_name):
		return Status.SUCCESS  # Already charged
	
	if debug_enabled:
		print("ChargeEquipmentAction: Charging ", equipment_name)
	
	ctx.emit_event("equipment_charge", {"equipment": equipment_name})
	return Status.SUCCESS
