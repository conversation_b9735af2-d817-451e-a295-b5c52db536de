extends RefCounted
class_name BehaviorNode

## Base class for all behavior tree nodes
##
## This is the foundation of the behavior tree system that replaces
## the complex decision logic in the old AI implementation. All nodes
## return a status indicating success, failure, or running state.
##
## Key Features:
## - Simple, composable behavior tree structure
## - Context-based access to game state
## - Performance monitoring capabilities
## - Clean separation of logic

## Behavior tree node execution results
enum Status {
	SUCCESS,    # Node completed successfully
	FAILURE,    # Node failed to complete
	RUNNING     # Node is still executing (for async operations)
}

## Node identification and debugging
var node_name: String = ""
var node_id: String = ""

## Execution context - provides access to game state and AI systems
var context: BehaviorContext

## Performance tracking
var execution_count: int = 0
var total_execution_time: float = 0.0
var last_execution_time: float = 0.0

## Debug settings
var debug_enabled: bool = false

func _init(name: String = "") -> void:
	node_name = name if name != "" else get_script().get_global_name()
	node_id = _generate_node_id()

## Core behavior tree interface - must be implemented by subclasses

func execute(ctx: BehaviorContext) -> Status:
	## Execute this behavior node with the given context
	## This is the main method that subclasses must implement
	
	context = ctx
	execution_count += 1
	
	var start_time = Time.get_unix_time_from_system()
	
	if debug_enabled:
		print("BehaviorNode: Executing ", node_name, " (", node_id, ")")
	
	# Call the actual implementation
	var result = _execute_implementation(ctx)
	
	# Track execution time
	var end_time = Time.get_unix_time_from_system()
	last_execution_time = end_time - start_time
	total_execution_time += last_execution_time
	
	if debug_enabled:
		print("BehaviorNode: ", node_name, " completed with ", _status_to_string(result), 
			  " in ", "%.3f" % (last_execution_time * 1000), "ms")
	
	return result

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Override this method in subclasses to implement behavior
	## Default implementation returns failure
	
	push_warning("BehaviorNode: _execute_implementation not implemented in " + node_name)
	return Status.FAILURE

## Utility methods

func reset() -> void:
	## Reset the node state - override in subclasses if needed
	pass

func get_status_string() -> String:
	## Get a human-readable status string for debugging
	
	return "Node: %s (ID: %s) - Executions: %d, Avg Time: %.3fms" % [
		node_name,
		node_id,
		execution_count,
		(total_execution_time / execution_count * 1000) if execution_count > 0 else 0.0
	]

func enable_debug(enable: bool = true) -> void:
	## Enable or disable debug output for this node
	debug_enabled = enable

func get_execution_stats() -> Dictionary:
	## Get execution statistics for performance monitoring
	
	return {
		"node_name": node_name,
		"node_id": node_id,
		"execution_count": execution_count,
		"total_execution_time": total_execution_time,
		"last_execution_time": last_execution_time,
		"average_execution_time": (total_execution_time / execution_count) if execution_count > 0 else 0.0
	}

## Private methods

func _generate_node_id() -> String:
	## Generate a unique ID for this node instance
	return node_name + "_" + str(randi() % 10000).pad_zeros(4)

func _status_to_string(status: Status) -> String:
	## Convert status enum to string for debugging
	
	match status:
		Status.SUCCESS:
			return "SUCCESS"
		Status.FAILURE:
			return "FAILURE"
		Status.RUNNING:
			return "RUNNING"
		_:
			return "UNKNOWN"


