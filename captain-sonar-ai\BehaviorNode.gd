extends RefCounted
class_name BehaviorNode

## Base class for all behavior tree nodes
##
## This is the foundation of the behavior tree system that replaces
## the complex decision logic in the old AI implementation. All nodes
## return a status indicating success, failure, or running state.
##
## Key Features:
## - Simple, composable behavior tree structure
## - Context-based access to game state
## - Performance monitoring capabilities
## - Clean separation of logic

## Behavior tree node execution results
enum Status {
	SUCCESS,    # Node completed successfully
	FAILURE,    # Node failed to complete
	RUNNING     # Node is still executing (for async operations)
}

## Node identification and debugging
var node_name: String = ""
var node_id: String = ""

## Execution context - provides access to game state and AI systems
var context: BehaviorContext

## Performance tracking
var execution_count: int = 0
var total_execution_time: float = 0.0
var last_execution_time: float = 0.0

## Debug settings
var debug_enabled: bool = false

func _init(name: String = "") -> void:
	node_name = name if name != "" else get_script().get_global_name()
	node_id = _generate_node_id()

## Core behavior tree interface - must be implemented by subclasses

func execute(ctx: BehaviorContext) -> Status:
	## Execute this behavior node with the given context
	## This is the main method that subclasses must implement
	
	context = ctx
	execution_count += 1
	
	var start_time = Time.get_unix_time_from_system()
	
	if debug_enabled:
		print("BehaviorNode: Executing ", node_name, " (", node_id, ")")
	
	# Call the actual implementation
	var result = _execute_implementation(ctx)
	
	# Track execution time
	var end_time = Time.get_unix_time_from_system()
	last_execution_time = end_time - start_time
	total_execution_time += last_execution_time
	
	if debug_enabled:
		print("BehaviorNode: ", node_name, " completed with ", _status_to_string(result), 
			  " in ", "%.3f" % (last_execution_time * 1000), "ms")
	
	return result

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Override this method in subclasses to implement behavior
	## Default implementation returns failure
	
	push_warning("BehaviorNode: _execute_implementation not implemented in " + node_name)
	return Status.FAILURE

## Utility methods

func reset() -> void:
	## Reset the node state - override in subclasses if needed
	pass

func get_status_string() -> String:
	## Get a human-readable status string for debugging
	
	return "Node: %s (ID: %s) - Executions: %d, Avg Time: %.3fms" % [
		node_name,
		node_id,
		execution_count,
		(total_execution_time / execution_count * 1000) if execution_count > 0 else 0.0
	]

func enable_debug(enable: bool = true) -> void:
	## Enable or disable debug output for this node
	debug_enabled = enable

func get_execution_stats() -> Dictionary:
	## Get execution statistics for performance monitoring
	
	return {
		"node_name": node_name,
		"node_id": node_id,
		"execution_count": execution_count,
		"total_execution_time": total_execution_time,
		"last_execution_time": last_execution_time,
		"average_execution_time": (total_execution_time / execution_count) if execution_count > 0 else 0.0
	}

## Private methods

func _generate_node_id() -> String:
	## Generate a unique ID for this node instance
	return node_name + "_" + str(randi() % 10000).pad_zeros(4)

func _status_to_string(status: Status) -> String:
	## Convert status enum to string for debugging
	
	match status:
		Status.SUCCESS:
			return "SUCCESS"
		Status.FAILURE:
			return "FAILURE"
		Status.RUNNING:
			return "RUNNING"
		_:
			return "UNKNOWN"

## Composite Node Base Class

class_name CompositeNode extends BehaviorNode

## Base class for nodes that have child nodes (Sequence, Selector, Parallel)

var children: Array[BehaviorNode] = []
var current_child_index: int = 0

func _init(name: String = "", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name)
	children = child_nodes

func add_child(child: BehaviorNode) -> void:
	## Add a child node to this composite
	children.append(child)

func remove_child(child: BehaviorNode) -> void:
	## Remove a child node from this composite
	children.erase(child)

func get_child_count() -> int:
	## Get the number of child nodes
	return children.size()

func reset() -> void:
	## Reset this node and all children
	super.reset()
	current_child_index = 0
	for child in children:
		child.reset()

## Decorator Node Base Class

class_name DecoratorNode extends BehaviorNode

## Base class for nodes that modify the behavior of a single child node

var child: BehaviorNode

func _init(name: String = "", child_node: BehaviorNode = null) -> void:
	super(name)
	child = child_node

func set_child(child_node: BehaviorNode) -> void:
	## Set the child node for this decorator
	child = child_node

func reset() -> void:
	## Reset this node and its child
	super.reset()
	if child:
		child.reset()

## Leaf Node Base Class

class_name LeafNode extends BehaviorNode

## Base class for leaf nodes (Actions and Conditions) that perform actual work

func _init(name: String = "") -> void:
	super(name)

# Leaf nodes don't have children, so they just implement _execute_implementation

## Behavior Context Class

class_name BehaviorContext extends RefCounted

## Context object that provides behavior nodes access to game state and AI systems
## This allows nodes to make decisions based on current game conditions

## References to AI systems
var ai_controller: AIController
var ai_event_bus: AIEventBus
var ai_personality: AIPersonality
var station_owner: Node  # The AI station that owns this behavior tree

## Game state information
var submarine_position: Vector2i
var submarine_health: int
var is_surfaced: bool
var current_turn: int

## Shared data between nodes in the same tree
var blackboard: Dictionary = {}

## Enemy intelligence
var enemy_position: Vector2i
var enemy_certainty: float = 0.0
var enemy_last_known_position: Vector2i

## Equipment status
var equipment_status: Dictionary = {}

## System status
var systems_damaged: Array[String] = []

func _init(controller: AIController, owner: Node) -> void:
	ai_controller = controller
	station_owner = owner
	
	if controller:
		ai_event_bus = controller.ai_event_bus
		ai_personality = controller.ai_personality

func update_game_state(position: Vector2i, health: int, surfaced: bool, turn: int) -> void:
	## Update the current game state information
	
	submarine_position = position
	submarine_health = health
	is_surfaced = surfaced
	current_turn = turn

func update_enemy_intelligence(position: Vector2i, certainty: float) -> void:
	## Update enemy position intelligence
	
	if certainty > enemy_certainty:
		enemy_position = position
		enemy_certainty = certainty
		enemy_last_known_position = position

func update_equipment_status(status: Dictionary) -> void:
	## Update equipment status information
	equipment_status = status

func update_system_status(damaged_systems: Array[String]) -> void:
	## Update system damage status
	systems_damaged = damaged_systems

func get_blackboard_value(key: String, default_value = null):
	## Get a value from the shared blackboard
	return blackboard.get(key, default_value)

func set_blackboard_value(key: String, value) -> void:
	## Set a value in the shared blackboard
	blackboard[key] = value

func clear_blackboard() -> void:
	## Clear all blackboard data
	blackboard.clear()

func is_enemy_detected() -> bool:
	## Check if we have recent enemy intelligence
	return enemy_certainty > 0.3

func is_enemy_close() -> bool:
	## Check if enemy is close to our position
	if not is_enemy_detected():
		return false
	
	var distance = submarine_position.distance_to(enemy_position)
	return distance <= 3.0

func is_health_critical() -> bool:
	## Check if submarine health is critically low
	return submarine_health <= 1

func is_system_damaged(system_name: String) -> bool:
	## Check if a specific system is damaged
	return system_name in systems_damaged

func can_use_equipment(equipment_name: String) -> bool:
	## Check if specific equipment is ready to use
	if not equipment_status.has(equipment_name):
		return false
	
	var status = equipment_status[equipment_name]
	return status.get("ready", false)

func get_personality_parameter(parameter_name: String) -> float:
	## Get a personality parameter value
	if not ai_personality:
		return 0.5  # Default value
	
	match parameter_name:
		"reaction_time":
			return ai_personality.reaction_time
		"accuracy":
			return ai_personality.accuracy
		"aggression":
			return ai_personality.aggression
		"caution":
			return ai_personality.caution
		"cooperation":
			return ai_personality.cooperation_level
		_:
			return 0.5

func should_make_optimal_decision() -> bool:
	## Check if AI should make optimal decision based on accuracy
	if ai_personality:
		return ai_personality.should_make_optimal_decision()
	return randf() < 0.5

func emit_event(event_type: String, data: Dictionary = {}) -> void:
	## Emit an event through the event bus
	if ai_event_bus:
		ai_event_bus.emit_event(event_type, data)
