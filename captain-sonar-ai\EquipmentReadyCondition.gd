class_name EquipmentReadyCondition extends ConditionNode

## Checks if specific equipment is ready to use

var equipment_name: String = ""

func _init(name: String = "EquipmentReady", equipment: String = "") -> void:
	super(name)
	equipment_name = equipment

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Check if equipment is ready
	if ctx.can_use_equipment(equipment_name):
		return Status.SUCCESS
	else:
		return Status.FAILURE
## Checks if specific equipment is ready to use

var equipment_name: String = ""

func _init(equipment: String) -> void:
	super("EquipmentReady_" + equipment)
	equipment_name = equipment

func _execute_implementation(ctx: BehaviorContext) -> Status:
	return Status.SUCCESS if ctx.can_use_equipment(equipment_name) else Status.FAILURE
