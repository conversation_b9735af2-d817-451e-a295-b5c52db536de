extends ConditionNode
class_name EquipmentReadyCondition

## Equipment Ready Condition
##
## Checks if specific equipment is ready to use

var equipment_name: String = ""

func _init(equipment: String) -> void:
	super("EquipmentReady_" + equipment)
	equipment_name = equipment

func _execute_implementation(ctx: BehaviorContext) -> Status:
	return Status.SUCCESS if ctx.can_use_equipment(equipment_name) else Status.FAILURE
