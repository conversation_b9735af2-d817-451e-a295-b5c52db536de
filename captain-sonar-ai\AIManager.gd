extends Node
class_name AIManager

# Central AI Manager that coordinates all AI stations
# Based on the C++ Captain <PERSON>ar AI implementation
# Fixes: Turn management, station synchronization, proper initialization
# New: Re-introduced and initialized mine and torpedo tracking variables for AICaptain

signal ai_action_completed(station_type: AIProfile.StationType, action: String)
signal ai_wants_to_surface()
signal ai_movement_decision(direction: String)
signal ai_turn_completed()

@export var ai_profile: AIProfile
@export var team_id: String = "bravo"  # AI controls the enemy team
@export var debug_mode: bool = false

# AI Station references
var ai_captain: AICaptain
var ai_engineer: AIEngineer
var ai_first_mate: AIFirstMate
var ai_radio_operator: AIRadioOperator

# Performance monitoring
var performance_monitor: AIPerformanceMonitor

# ===========================
# --- Shared AI data (matching C++ global variables) ---
# ===========================
var ai_health: int = 4
var surface_time: int = 0 # Not fully implemented for calculation, but kept for C++ parity
var should_surface: bool = false
# ai_called array is typically used for tracking if specific AI actions were performed.
# Not directly used in the current Godot flow for core logic, but good for parity.
var ai_called: Array[bool] = [false, false, false, false, false, false, false, false]
# wants array for captain's desires (guns, detection, silence)
var wants: Array[bool] = [false, false, false]

# Missing C++ global variables re-added for AICaptain's expectations
# Mine tracking (5 mines max, each entry is [x,y] coordinates)
var mines_position: Array = []
var mine_count: int = 0
var active_mine: int = 0 # Currently active mine index

# User torpedo position for AI tracking (stored as [x, y])
var user_torpedo_position: Array[int] = [0, 0]

# Radio operator's tried spaces (15x15 bool array for radio operator's search)
var ai_radio_op_tried_spaces: Array = [] 

# Game state flags (matching C++ bools) - some might be managed by GameState directly
var map_chosen: bool = false
var ai_surfaced: bool = false
var ai_first_position: bool = false # Not directly used in current Godot AI
var keep_user_position: bool = false # Managed by AIRadioOperator internally
var added: bool = false # Not directly used in current Godot AI
var write_path_done: bool = false # Managed by AICaptain internally
var menu_done: bool = false # Game UI state, not AI
var game_over: bool = false
var movement_done: bool = false # Managed by GameState
var ai_called_stop: bool = true # Not directly used in current Godot AI

# Map and pathfinding data (mostly handled by GameState or specific AI stations now)
var ai_map: Array = [] # 15x15 bool array for AI path tracking (AI Captain/Engineer used this)
var ai_path: Array = [] # Array of Vector2i positions
var ai_compass: Array[String] = [] # Direction strings for each move
var next_path: int = 0

# Equipment states (synced with AIFirstMate)
var equipment_current: Dictionary = {
	"mines": 0,
	"torpedo": 0,
	"drones": 0,
	"sonar": 0,
	"silence": 0,
	"scenario": 0
}

var equipment_max: Dictionary = {
	"mines": 3,
	"torpedo": 4,
	"drones": 3,
	"sonar": 3,
	"silence": 6,
	"scenario": 1
}

# System damage tracking (synced with AIEngineer)
var north_damage: Array[bool] = [false, false, false, false, false, false]
var south_damage: Array[bool] = [false, false, false, false, false, false]
var east_damage: Array[bool] = [false, false, false, false, false, false]
var west_damage: Array[bool] = [false, false, false, false, false, false]

var guns_broke: bool = false
var detection_broke: bool = false
var special_broke: bool = false

# ===========================
# --- Turn Management ---
# ===========================
# Tracks if individual stations have completed their current task in a turn
var stations_ready: Dictionary = {
	"captain": true,
	"engineer": true,
	"first_mate": true,
	"radio_operator": true
}
var completion_timer_active: bool = false

func _ready() -> void:
	if not ai_profile:
		ai_profile = AIProfile.new()

	# IMPORTANT: Initialize mine and torpedo arrays BEFORE setting up AICaptain,
	# as AICaptain.setup() attempts to read these properties.
	_initialize_mine_and_torpedo_data()

	_initialize_ai_systems()
	_setup_equipment_from_profile()
	_setup_performance_monitoring()
	
	# Connect to GameState if available, to sync submarine state
	if GameState and not GameState.is_connected("state_updated", _on_game_state_updated):
		GameState.connect("state_updated", _on_game_state_updated)
	
	if debug_mode:
		print("AI Manager: Initialization complete for team ", team_id)

func _initialize_mine_and_torpedo_data() -> void:
	"""Initializes arrays for mine positions and user torpedo position."""
	mines_position.clear()
	# Allocate space for up to 5 mines, each initially at [0,0]
	for i in range(5):
		mines_position.append([0, 0])
	mine_count = 0
	active_mine = 0 # No active mine initially
	
	# Initialize user torpedo position
	user_torpedo_position = [0, 0] # Default to [0,0]
	
	# Initialize map data for AI pathfinding and radio operator
	ai_map.clear()
	ai_radio_op_tried_spaces.clear()
	for x in range(15):
		ai_map.append([])
		ai_radio_op_tried_spaces.append([])
		for y in range(15):
			ai_map[x].append(false)
			ai_radio_op_tried_spaces[x].append(false)

	# Initialize path tracking
	ai_path.clear()
	ai_compass.clear()
	for i in range(50): # Match C++ array size for path history
		ai_path.append(Vector2i.ZERO)
		ai_compass.append("")
	next_path = 0 # Reset path index

func _initialize_ai_systems() -> void:
	"""Creates, configures, and connects signals for all AI station instances."""
	# Create station instances
	ai_captain = AICaptain.new()
	ai_engineer = AIEngineer.new()
	ai_first_mate = AIFirstMate.new()
	ai_radio_operator = AIRadioOperator.new()
	
	# Add them as children of the AIManager to enable `await` and signal connections
	add_child(ai_captain)
	add_child(ai_engineer)
	add_child(ai_first_mate)
	add_child(ai_radio_operator)
	
	# Configure each station with the new AI system
	# Get or create AIController instance
	var ai_controller = AIController.get_instance()

	# Setup stations with the new architecture
	if ai_controller and ai_controller.ai_personality and ai_controller.ai_event_bus and ai_controller.ai_behavior_manager:
		ai_captain.setup(ai_controller.ai_personality, ai_controller.ai_event_bus, ai_controller.ai_behavior_manager)
		ai_engineer.setup(ai_controller.ai_personality, ai_controller.ai_event_bus, ai_controller.ai_behavior_manager)
		ai_first_mate.setup(ai_controller.ai_personality, ai_controller.ai_event_bus, ai_controller.ai_behavior_manager)
		ai_radio_operator.setup(ai_controller.ai_personality, ai_controller.ai_event_bus, ai_controller.ai_behavior_manager)
	else:
		print("AIManager: Warning - AIController not properly initialized, using fallback setup")
		# Fallback - create minimal systems for compatibility
		var fallback_personality = AIPersonality.new()
		var fallback_event_bus = AIEventBus.new()
		var fallback_behavior_manager = AIBehaviorManager.new()
		add_child(fallback_event_bus)
		add_child(fallback_behavior_manager)
		fallback_behavior_manager.setup(fallback_personality, fallback_event_bus)

		ai_captain.setup(fallback_personality, fallback_event_bus, fallback_behavior_manager)
		ai_engineer.setup(fallback_personality, fallback_event_bus, fallback_behavior_manager)
		ai_first_mate.setup(fallback_personality, fallback_event_bus, fallback_behavior_manager)
		ai_radio_operator.setup(fallback_personality, fallback_event_bus, fallback_behavior_manager)
	
	# Connect station signals to AIManager's handlers
	ai_captain.movement_decision.connect(_on_captain_movement_decision)
	ai_engineer.surface_recommended.connect(_on_engineer_surface_recommendation)
	ai_first_mate.equipment_ready.connect(_on_first_mate_equipment_ready)
	ai_radio_operator.enemy_position_updated.connect(_on_radio_enemy_position_updated)
	
	if debug_mode:
		print("AI Manager: All stations initialized and connected")

func _setup_equipment_from_profile() -> void:
	"""Sets the maximum equipment capacities based on the AI profile."""
	if ai_profile:
		equipment_max["mines"] = ai_profile.max_mines
		equipment_max["torpedo"] = ai_profile.max_torpedo
		equipment_max["drones"] = ai_profile.max_drones
		equipment_max["sonar"] = ai_profile.max_sonar
		equipment_max["silence"] = ai_profile.max_silence
		equipment_max["scenario"] = ai_profile.max_scenario
	if debug_mode:
		print("AI Manager: Equipment max capacities set from profile.")

func _setup_performance_monitoring() -> void:
	"""Initialize performance monitoring system"""
	performance_monitor = AIPerformanceMonitor.new()
	performance_monitor.enable_monitoring = debug_mode  # Enable monitoring in debug mode
	add_child(performance_monitor)

	if debug_mode:
		performance_monitor.connect("performance_report_ready", _on_performance_report)

func _on_performance_report(report: Dictionary) -> void:
	"""Handle performance reports"""
	if debug_mode:
		print("AI Performance - Efficiency Score: ", "%.1f" % report["efficiency_score"], "/100")

func start_ai_turn() -> void:
	"""Initiates a coordinated AI turn, starting all active stations' processing."""
	if game_over:
		return

	# Start performance monitoring
	if performance_monitor:
		performance_monitor.start_turn_monitoring()

	if debug_mode:
		print("AI Manager: Starting coordinated AI turn for team ", team_id)

	# Reset all station 'done' flags to indicate they are starting work
	stations_ready["captain"] = false
	stations_ready["engineer"] = false
	stations_ready["first_mate"] = false
	stations_ready["radio_operator"] = false

	# Start each station's main processing method in parallel
	# Only start if the station is not already processing
	if ai_captain and ai_captain.get_turn_complete_status():
		if performance_monitor:
			performance_monitor.start_station_monitoring("captain")
		ai_captain.start_turn()

	if ai_engineer and ai_engineer.get_turn_complete_status():
		if performance_monitor:
			performance_monitor.start_station_monitoring("engineer")
		ai_engineer.start_turn()

	if ai_first_mate and ai_first_mate.get_turn_complete_status():
		if performance_monitor:
			performance_monitor.start_station_monitoring("first_mate")
		ai_first_mate.start_turn()

	if ai_radio_operator and ai_radio_operator.get_turn_complete_status():
		if performance_monitor:
			performance_monitor.start_station_monitoring("radio_operator")
		ai_radio_operator.start_turn()

	# Start monitoring for completion
	_monitor_turn_completion()

func _monitor_turn_completion() -> void:
	"""Efficiently monitors for turn completion using a single timer check"""
	# Use a lightweight timer to check completion status
	if completion_timer_active:
		return
	completion_timer_active = true
	var completion_timer = get_tree().create_timer(0.1, false)
	completion_timer.timeout.connect(_check_turn_completion)

func _check_turn_completion() -> void:
	"""Checks if all AI stations have completed their turn and signals completion"""
	var all_ready = true
	for station_name in stations_ready:
		if not stations_ready[station_name]:
			all_ready = false
			break

	if all_ready:
		# End performance monitoring
		if performance_monitor:
			performance_monitor.end_turn_monitoring()

		if debug_mode:
			print("AI Manager: All stations completed their turn")
		_on_turn_completed()
		emit_signal("ai_turn_completed")
		completion_timer_active = false

func _on_turn_completed() -> void:
	"""
	Actions to take when all AI stations have finished their work for a turn.
	This method is currently less critical as GameState manages the overall turn
	progression based on fixed delays, but it's a good placeholder.
	"""
	if debug_mode:
		print("AI Manager: Consolidated turn actions complete.")
	
	# Re-evaluate surfacing condition after all stations have processed
	if should_surface and not ai_surfaced:
		_execute_surface()
	
	# Update equipment charging passively
	_update_equipment_charging()

func _execute_surface() -> void:
	"""Handles the AI submarine surfacing procedure."""
	if debug_mode:
		print("AI Manager: AI executing surface maneuver")

	ai_surfaced = true
	should_surface = false # Reset the flag now that surfacing is being executed

	# Calculate surface time (simplified from C++ logic)
	# This part needs to read the actual damage states from Engineer to be accurate.
	# For now, it's a fixed value or simple calculation.
	var base_time = 3 # seconds
	var damage_penalty = 0 
	# Example: If guns_broke, damage_penalty += 1; if detection_broke, damage_penalty += 1; etc.
	# This would require Engineer to update AIManager's guns_broke/detection_broke,
	# or AIManager to query Engineer directly.
	surface_time = base_time + damage_penalty

	# Notify radio operator that enemy is surfacing (to clear tracking)
	if ai_radio_operator:
		ai_radio_operator.handle_enemy_surface()

	# Clear damage systems on surface (as per C++ doSurface function)
	_reset_damage_systems()
	# Preserve equipment state (no equipment is lost by surfacing currently)
	# _preserve_equipment_state() # Not fully implemented in Godot, just a placeholder

	# Update GameState about surfacing
	if GameState and GameState.teams.has(team_id):
		var ai_state: SubmarineState = GameState.teams[team_id]
		ai_state.is_surfaced = true
		GameState.emit_signal("state_updated", team_id)

	if debug_mode:
		print("AI Manager: Surfaced for ", surface_time, " seconds")

	# Wait for the surfacing duration
	await get_tree().create_timer(float(surface_time)).timeout

	# Submerge again
	ai_surfaced = false
	if GameState and GameState.teams.has(team_id):
		var ai_state: SubmarineState = GameState.teams[team_id]
		ai_state.is_surfaced = false
		GameState.emit_signal("state_updated", team_id)

	if debug_mode:
		print("AI Manager: Submerged - resuming operations")

func _update_equipment_charging() -> void:
	"""
	Passively charges equipment over time. This is separate from First Mate's active
	charging and simulates general replenishment or slower charging.
	"""
	for equipment_name in equipment_current:
		if equipment_current[equipment_name] < equipment_max[equipment_name]:
			# A small chance to charge each turn (simulates background charging)
			# This is very simplified. First Mate's actions are primary charging.
			if randf() < 0.2: # 20% chance per frame to charge by 1
				equipment_current[equipment_name] += 1
				# Clamp to max
				equipment_current[equipment_name] = min(equipment_current[equipment_name], equipment_max[equipment_name])
				if debug_mode:
					print("AI Manager: Background charged ", equipment_name, ". Current: ", equipment_current[equipment_name])

func _reset_damage_systems() -> void:
	"""Resets all damage flags when the submarine surfaces."""
	north_damage = [false, false, false, false, false, false]
	south_damage = [false, false, false, false, false, false]
	east_damage = [false, false, false, false, false, false]
	west_damage = [false, false, false, false, false, false]
	
	guns_broke = false
	detection_broke = false
	special_broke = false
	
	if debug_mode:
		print("AI Manager: All damage systems reset.")

# ===========================
# --- Signal Handlers ---
# ===========================
func _on_game_state_updated(updated_team_id: String) -> void:
	"""Handles updates from the global GameState."""
	if updated_team_id == team_id:
		_sync_with_game_state()

func _sync_with_game_state() -> void:
	"""Synchronizes AI Manager's internal state with the global GameState."""
	if not GameState or not GameState.teams.has(team_id):
		return
	
	var ai_state: SubmarineState = GameState.teams[team_id]
	ai_health = ai_state.health
	ai_surfaced = ai_state.is_surfaced
	# You might also sync position and other critical info here if needed by AIManager directly

func _on_captain_movement_decision(direction: String) -> void:
	"""Handles the Captain's decision to move and propagates it to GameState."""
	if performance_monitor:
		performance_monitor.end_station_monitoring("captain")
		performance_monitor.track_signal_emission()

	if debug_mode:
		print("Manager: AI Captain decided to move: ", direction)

	stations_ready["captain"] = true # Mark captain's task as complete
	emit_signal("ai_movement_decision", direction) # GameState listens to this

func _on_engineer_surface_recommendation(should_surface_now: bool) -> void:
	"""Handles Engineer's recommendation to surface."""
	if performance_monitor:
		performance_monitor.end_station_monitoring("engineer")
		performance_monitor.track_signal_emission()

	should_surface = should_surface_now
	stations_ready["engineer"] = true # Mark engineer's task as complete

	if should_surface:
		if debug_mode:
			print("Manager: AI Engineer recommends surfacing.")
		emit_signal("ai_wants_to_surface") # GameState listens to this

func _on_first_mate_equipment_ready(equipment_name: String) -> void:
	"""Handles First Mate's notification that a piece of equipment is ready."""
	if performance_monitor:
		performance_monitor.end_station_monitoring("first_mate")
		performance_monitor.track_signal_emission()

	# Ensure the equipment count is at max as reported
	equipment_current[equipment_name] = equipment_max[equipment_name]
	stations_ready["first_mate"] = true # Mark first mate's task as complete

	if debug_mode:
		print("Manager: AI First Mate reports equipment ready: ", equipment_name)

	emit_signal("ai_action_completed", AIProfile.StationType.FIRST_MATE, equipment_name + "_ready")

func _on_radio_enemy_position_updated(estimated_position: Vector2i, certainty: float) -> void:
	"""Handles Radio Operator's update on enemy position."""
	if performance_monitor:
		performance_monitor.end_station_monitoring("radio_operator")
		performance_monitor.track_signal_emission()

	stations_ready["radio_operator"] = true # Mark radio operator's task as complete

	# Share this information with the AI Captain for tactical decisions
	if ai_captain:
		ai_captain.update_enemy_position(estimated_position, certainty)
	
	if debug_mode:
		print("Manager: AI Radio Operator estimates enemy at: ", estimated_position, 
			  " (certainty: ", "%.2f" % certainty, ")") # Format certainty for clearer log

# ===========================
# --- Public Interface for GameState / UI ---
# ===========================
func get_current_position() -> Vector2i:
	"""Retrieves the AI's current submarine position from GameState."""
	if GameState and GameState.teams.has(team_id):
		return GameState.teams[team_id].position
	return Vector2i.ZERO

func get_map_data() -> Array:
	"""Retrieves the current map data for collision detection from GameState."""
	if GameState:
		return GameState.map_data
	return []

func is_position_valid(pos: Vector2i) -> bool:
	"""
	Checks if a given position is within map bounds and is not an island.
	This is crucial for pathfinding and movement validation for all AI stations.
	"""
	var map_data_ref = get_map_data()
	
	# Check against dynamically retrieved map dimensions
	var map_width = map_data_ref.size()
	var map_height = map_data_ref[0].size() if map_width > 0 else 0

	if pos.x < 0 or pos.x >= map_width or pos.y < 0 or pos.y >= map_height:
		return false # Out of bounds

	if map_width > 0 and map_height > 0:
		# false = water, true = island. Return true if it's water (not an island)
		return not map_data_ref[pos.x][pos.y]
	
	return true # If no map data, assume all positions are valid (but this shouldn't happen)

func set_game_over(is_over: bool) -> void:
	"""Sets the game over state, propagating it to all AI stations."""
	game_over = is_over
	if ai_captain:
		ai_captain.set_game_over(is_over)
	if ai_engineer:
		ai_engineer.set_game_over(is_over)
	if ai_first_mate:
		ai_first_mate.set_game_over(is_over)
	if ai_radio_operator:
		ai_radio_operator.set_game_over(is_over)
	if debug_mode:
		print("AI Manager: Game Over state set to ", is_over)

func get_ai_status() -> Dictionary:
	"""Returns a dictionary with the current status of the AI team."""
	return {
		"enabled": true, # AIManager itself implies AI is enabled
		"team_id": team_id,
		"health": ai_health,
		"surfaced": ai_surfaced,
		"equipment": equipment_current.duplicate(),
		"systems": {
			"guns_broke": guns_broke, # More direct status of broke systems
			"detection_broke": detection_broke,
			"special_broke": special_broke
		},
		"radio_certainty": ai_radio_operator.get_certainty() if ai_radio_operator else 0.0,
		"stations_ready": stations_ready.duplicate() # Show if stations are done processing
	}

# ===========================
# --- Utility functions (matching C++ implementation) ---
# ===========================
# Note: Many of these were designed for C++ specific grid/array access patterns
# and might be more cleanly handled via `is_position_valid` or dedicated pathfinding in Godot.
# Keeping for parity if they have specific C++ behavior not covered by `is_position_valid`.

func no_no_areas(y: int, x: int, player: bool = false, empty: bool = false) -> bool:
	"""
	Checks if a given (y,x) coordinate is a "no-go" area (out of bounds, island, or
	already visited by AI, depending on flags).
	This function is very similar to `is_position_valid` but with more granular
	control from the original C++ implementation.
	"""
	var area_clear: bool = true

	# Bounds check (y and x are 0-14, -1 or 15 means out of bounds)
	if y < 0 or x < 0 or y >= 15 or x >= 15:
		area_clear = false
	
	# Check against islands if not 'empty' check (e.g., placing mines)
	if area_clear and not empty and GameState: # Use GameState's map_data
		var map_data_ref = GameState.map_data
		if map_data_ref.size() > x and map_data_ref[x].size() > y:
			if map_data_ref[x][y]:  # true = island, false = water
				area_clear = false

	# Check against AI's own path if not player's movement and not 'empty' check
	# This prevents AI from moving back into its immediate recent path
	if area_clear and not player and not empty and ai_map.size() > y and ai_map[y].size() > x:
		if ai_map[y][x]:  # If AI has marked this spot as visited
			area_clear = false

	return area_clear

func grab_sector_x(sector: int) -> int:
	"""Grabs the min X coordinate of a given sector (1-9)."""
	match sector:
		1, 4, 7: return 0
		2, 5, 8: return 5
		3, 6, 9: return 10
		_: return 0 # Should ideally not happen with valid sector input

func grab_sector_y(sector: int) -> int:
	"""Grabs the min Y coordinate of a given sector (1-9)."""
	match sector:
		1, 2, 3: return 0
		4, 5, 6: return 5
		7, 8, 9: return 10
		_: return 0 # Should ideally not happen with valid sector input

func sector_positions_check(y: int, x: int, sector: int) -> bool:
	"""Checks if a given position (y,x) falls within a specified sector (1-9)."""
	match sector:
		1: return y >= 0 and y <= 4 and x >= 0 and x <= 4
		2: return y >= 0 and y <= 4 and x >= 5 and x <= 9
		3: return y >= 0 and y <= 4 and x >= 10 and x <= 14
		4: return y >= 5 and y <= 9 and x >= 0 and x <= 4
		5: return y >= 5 and y <= 9 and x >= 5 and x <= 9
		6: return y >= 5 and y <= 9 and x >= 10 and x <= 14
		7: return y >= 10 and y <= 14 and x >= 0 and x <= 4
		8: return y >= 10 and y <= 14 and x >= 5 and x <= 9
		9: return y >= 10 and y <= 14 and x >= 10 and x <= 14
		_: return false # Invalid sector

func sector_positions(y: int, x: int) -> int:
	"""Returns the sector number (1-9) that the given coordinates fall into."""
	if y >= 0 and y <= 4:
		if x >= 0 and x <= 4: return 1
		if x >= 5 and x <= 9: return 2
		if x >= 10 and x <= 14: return 3
	elif y >= 5 and y <= 9:
		if x >= 0 and x <= 4: return 4
		if x >= 5 and x <= 9: return 5
		if x >= 10 and x <= 14: return 6
	elif y >= 10 and y <= 14:
		if x >= 0 and x <= 4: return 7
		if x >= 5 and x <= 9: return 8
		if x >= 10 and x <= 14: return 9
	return 0 # Invalid position

func torpedo_check(start_y: int, start_x: int, end_y: int, end_x: int) -> bool:
	"""
	Checks if a torpedo path from (start_x, start_y) to (end_x, end_y) is clear of land.
	This is a simplified pathfinding check for torpedoes.
	"""
	var x_curr = start_x
	var y_curr = start_y
	var max_steps = 15 # Max range for torpedo
	
	for i in range(max_steps):
		if x_curr == end_x and y_curr == end_y:
			return true # Reached target

		var x_diff = end_x - x_curr
		var y_diff = end_y - y_curr

		var moved = false
		if abs(x_diff) > abs(y_diff):
			if x_diff > 0: x_curr += 1 # Move East
			else: x_curr -= 1 # Move West
			moved = true
		else:
			if y_diff > 0: y_curr += 1 # Move South
			else: y_curr -= 1 # Move North
			moved = true

		if not moved: # If no movement could be decided, break (e.g., already at target, or error)
			break

		# Check if the current step is valid (not an island, within bounds)
		if not is_position_valid(Vector2i(x_curr, y_curr)):
			return false # Hit land or out of bounds

	return false # Did not reach target within max_steps or hit an obstacle

func write_path(pre_y: int, pre_x: int) -> void:
	"""
	Records the AI's path into `ai_map` and `ai_path`.
	This is used by the AI Captain to track where it has moved.
	"""
	# Check bounds and mark position as visited on the AI's internal map
	if pre_y >= 0 and pre_y < 15 and pre_x >= 0 and pre_x < 15:
		ai_map[pre_y][pre_x] = true

	# Store the position in the path history
	if next_path < ai_path.size():
		ai_path[next_path] = Vector2i(pre_x, pre_y)
		next_path += 1
	else:
		if debug_mode:
			print("AI Manager: Warning - ai_path array is full, cannot write more path.")

	write_path_done = true # Mark that path writing occurred this turn
