extends ActionNode
class_name ReviewDataAction

## Review Data Action
##
## Action to review collected data

func _init() -> void:
	super("ReviewData")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("ReviewDataAction: Reviewing collected data")
	
	ctx.emit_event("data_review", {
		"station": "radiooperator",
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
