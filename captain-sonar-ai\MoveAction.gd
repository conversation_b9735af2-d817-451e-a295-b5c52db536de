extends ActionNode
class_name MoveAction

## Move Action
##
## Action to move in a specific direction

var direction: String = ""

func _init(move_direction: String) -> void:
	super("Move_" + move_direction)
	direction = move_direction

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if direction == "":
		return Status.FAILURE
	
	if debug_enabled:
		print("MoveAction: Moving ", direction)
	
	ctx.emit_event("movement_decision", {"direction": direction})
	return Status.SUCCESS
