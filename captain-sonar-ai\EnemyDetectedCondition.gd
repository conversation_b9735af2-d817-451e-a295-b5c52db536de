extends ConditionNode
class_name EnemyDetectedCondition

## Enemy Detected Condition
##
## Checks if enemy has been detected with sufficient certainty

var minimum_certainty: float = 0.3

func _init(min_certainty: float = 0.3) -> void:
	super("EnemyDetected")
	minimum_certainty = min_certainty

func _execute_implementation(ctx: BehaviorContext) -> Status:
	return Status.SUCCESS if ctx.enemy_certainty >= minimum_certainty else Status.FAILURE
