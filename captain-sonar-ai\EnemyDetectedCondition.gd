class_name EnemyDetectedCondition extends ConditionNode

## Checks if enemy has been detected

func _init(name: String = "EnemyDetected") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Check if enemy is detected
	if ctx.is_enemy_detected():
		return Status.SUCCESS
	else:
		return Status.FAILURE
## Checks if enemy has been detected with sufficient certainty

var minimum_certainty: float = 0.3

func _init(min_certainty: float = 0.3) -> void:
	super("EnemyDetected")
	minimum_certainty = min_certainty

func _execute_implementation(ctx: BehaviorContext) -> Status:
	return Status.SUCCESS if ctx.enemy_certainty >= minimum_certainty else Status.FAILURE
