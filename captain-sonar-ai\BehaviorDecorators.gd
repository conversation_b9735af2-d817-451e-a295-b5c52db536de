extends RefCounted

## Decorator Behavior Tree Nodes
##
## This file contains decorator nodes that modify the behavior of their
## single child node. These provide control flow modifications like
## inversion, repetition, cooldowns, and conditional execution.

## Inverter Node - inverts the result of its child
class_name InverterNode extends DecoratorNode

## Inverts SUCCESS to FAILURE and FA<PERSON>UR<PERSON> to SUCCESS.
## RUNNING remains RUNNING.

func _init(name: String = "Inverter", child_node: BehaviorNode = null) -> void:
	super(name, child_node)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child and invert the result
	
	if not child:
		return Status.FAILURE
	
	var result = child.execute(ctx)
	
	match result:
		Status.SUCCESS:
			return Status.FAILURE
		Status.FAILURE:
			return Status.SUCCESS
		Status.RUNNING:
			return Status.RUNNING
		_:
			return Status.FAILURE

## Repeater Node - repeats its child a specified number of times
class_name RepeaterNode extends DecoratorNode

## Repeats the child node execution. Can repeat a fixed number of times
## or until a specific condition is met.

var repeat_count: int = 1
var current_repetition: int = 0
var repeat_until_failure: bool = false
var repeat_until_success: bool = false

func _init(name: String = "Repeater", child_node: BehaviorNode = null, 
		   count: int = 1, until_failure: bool = false, until_success: bool = false) -> void:
	super(name, child_node)
	repeat_count = count
	repeat_until_failure = until_failure
	repeat_until_success = until_success

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child repeatedly based on configuration
	
	if not child:
		return Status.FAILURE
	
	while true:
		var result = child.execute(ctx)
		
		# Handle running state
		if result == Status.RUNNING:
			return Status.RUNNING
		
		current_repetition += 1
		
		# Check termination conditions
		if repeat_until_failure and result == Status.FAILURE:
			current_repetition = 0
			return Status.SUCCESS
		
		if repeat_until_success and result == Status.SUCCESS:
			current_repetition = 0
			return Status.SUCCESS
		
		# Check repeat count
		if not repeat_until_failure and not repeat_until_success:
			if current_repetition >= repeat_count:
				current_repetition = 0
				return result
		
		# Reset child for next iteration
		child.reset()
		
		# Prevent infinite loops in single frame
		if current_repetition > 100:
			push_warning("RepeaterNode: Breaking potential infinite loop")
			current_repetition = 0
			return Status.FAILURE

func reset() -> void:
	## Reset repeater state
	super.reset()
	current_repetition = 0

## Cooldown Node - prevents child execution until cooldown expires
class_name CooldownNode extends DecoratorNode

## Prevents the child from executing until a cooldown period has elapsed.
## Useful for rate-limiting expensive operations or adding realistic delays.

var cooldown_duration: float = 1.0
var last_execution_time: float = 0.0
var return_running_during_cooldown: bool = false

func _init(name: String = "Cooldown", child_node: BehaviorNode = null, 
		   duration: float = 1.0, running_during_cooldown: bool = false) -> void:
	super(name, child_node)
	cooldown_duration = duration
	return_running_during_cooldown = running_during_cooldown

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child only if cooldown has expired
	
	if not child:
		return Status.FAILURE
	
	var current_time = Time.get_unix_time_from_system()
	
	# Check if cooldown has expired
	if current_time - last_execution_time < cooldown_duration:
		return Status.RUNNING if return_running_during_cooldown else Status.FAILURE
	
	# Execute child
	var result = child.execute(ctx)
	
	# Update last execution time only on completion
	if result != Status.RUNNING:
		last_execution_time = current_time
	
	return result

func reset() -> void:
	## Reset cooldown (allows immediate execution)
	super.reset()
	last_execution_time = 0.0

## Timeout Node - fails child if it takes too long
class_name TimeoutNode extends DecoratorNode

## Fails the child if it doesn't complete within the specified timeout.
## Useful for preventing AI from getting stuck on long-running operations.

var timeout_duration: float = 5.0
var start_time: float = 0.0
var is_timing: bool = false

func _init(name: String = "Timeout", child_node: BehaviorNode = null, duration: float = 5.0) -> void:
	super(name, child_node)
	timeout_duration = duration

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child with timeout protection
	
	if not child:
		return Status.FAILURE
	
	var current_time = Time.get_unix_time_from_system()
	
	# Start timing on first execution
	if not is_timing:
		start_time = current_time
		is_timing = true
	
	# Check for timeout
	if current_time - start_time > timeout_duration:
		is_timing = false
		child.reset()
		return Status.FAILURE
	
	# Execute child
	var result = child.execute(ctx)
	
	# Stop timing when child completes
	if result != Status.RUNNING:
		is_timing = false
	
	return result

func reset() -> void:
	## Reset timeout state
	super.reset()
	is_timing = false
	start_time = 0.0

## Retry Node - retries child on failure
class_name RetryNode extends DecoratorNode

## Retries the child node a specified number of times on failure.
## Useful for handling temporary failures or adding persistence.

var max_retries: int = 3
var current_retry: int = 0
var retry_delay: float = 0.0
var last_retry_time: float = 0.0

func _init(name: String = "Retry", child_node: BehaviorNode = null, 
		   retries: int = 3, delay: float = 0.0) -> void:
	super(name, child_node)
	max_retries = retries
	retry_delay = delay

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child with retry logic
	
	if not child:
		return Status.FAILURE
	
	# Handle retry delay
	if retry_delay > 0.0 and current_retry > 0:
		var current_time = Time.get_unix_time_from_system()
		if current_time - last_retry_time < retry_delay:
			return Status.RUNNING
	
	# Execute child
	var result = child.execute(ctx)
	
	match result:
		Status.SUCCESS:
			current_retry = 0
			return Status.SUCCESS
		Status.RUNNING:
			return Status.RUNNING
		Status.FAILURE:
			current_retry += 1
			last_retry_time = Time.get_unix_time_from_system()
			
			if current_retry <= max_retries:
				child.reset()
				return Status.RUNNING  # Continue trying
			else:
				current_retry = 0
				return Status.FAILURE  # Give up
	
	return Status.FAILURE

func reset() -> void:
	## Reset retry state
	super.reset()
	current_retry = 0
	last_retry_time = 0.0

## Condition Node - executes child only if condition is met
class_name ConditionNode extends DecoratorNode

## Executes the child only if a specified condition is true.
## The condition is evaluated each time the node is executed.

var condition_callback: Callable
var invert_condition: bool = false

func _init(name: String = "Condition", child_node: BehaviorNode = null, 
		   condition: Callable = Callable(), invert: bool = false) -> void:
	super(name, child_node)
	condition_callback = condition
	invert_condition = invert

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child only if condition is met
	
	if not child:
		return Status.FAILURE
	
	# Evaluate condition
	var condition_met = false
	if condition_callback.is_valid():
		try:
			condition_met = condition_callback.call(ctx)
		except:
			if debug_enabled:
				print("ConditionNode: Error evaluating condition")
			return Status.FAILURE
	
	# Apply inversion if needed
	if invert_condition:
		condition_met = not condition_met
	
	# Execute child if condition is met
	if condition_met:
		return child.execute(ctx)
	else:
		return Status.FAILURE

## Probability Node - executes child based on probability
class_name ProbabilityNode extends DecoratorNode

## Executes the child based on a probability check.
## Useful for adding randomness to AI behavior.

var success_probability: float = 0.5
var check_on_each_execution: bool = true
var probability_passed: bool = false

func _init(name: String = "Probability", child_node: BehaviorNode = null, 
		   probability: float = 0.5, check_each_time: bool = true) -> void:
	super(name, child_node)
	success_probability = clamp(probability, 0.0, 1.0)
	check_on_each_execution = check_each_time

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child based on probability
	
	if not child:
		return Status.FAILURE
	
	# Check probability
	if check_on_each_execution or not probability_passed:
		probability_passed = randf() < success_probability
	
	if probability_passed:
		var result = child.execute(ctx)
		
		# Reset probability check when child completes
		if result != Status.RUNNING:
			probability_passed = false
		
		return result
	else:
		return Status.FAILURE

func reset() -> void:
	## Reset probability state
	super.reset()
	probability_passed = false

## Rate Limiter Node - limits child execution frequency
class_name RateLimiterNode extends DecoratorNode

## Limits how frequently the child can be executed.
## Different from cooldown in that it tracks execution rate over time.

var max_executions_per_second: float = 1.0
var execution_times: Array[float] = []

func _init(name: String = "RateLimiter", child_node: BehaviorNode = null, rate: float = 1.0) -> void:
	super(name, child_node)
	max_executions_per_second = rate

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child if rate limit allows
	
	if not child:
		return Status.FAILURE
	
	var current_time = Time.get_unix_time_from_system()
	
	# Clean old execution times (older than 1 second)
	execution_times = execution_times.filter(func(time): return current_time - time < 1.0)
	
	# Check rate limit
	if execution_times.size() >= max_executions_per_second:
		return Status.FAILURE
	
	# Execute child
	var result = child.execute(ctx)
	
	# Record execution time on completion
	if result != Status.RUNNING:
		execution_times.append(current_time)
	
	return result

func reset() -> void:
	## Reset rate limiter
	super.reset()
	execution_times.clear()

## Success Node - always returns success after child execution
class_name AlwaysSucceedNode extends DecoratorNode

## Always returns SUCCESS regardless of child result.
## Useful for ensuring a branch always succeeds.

func _init(name: String = "AlwaysSucceed", child_node: BehaviorNode = null) -> void:
	super(name, child_node)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child and return success
	
	if not child:
		return Status.SUCCESS
	
	var result = child.execute(ctx)
	
	# Return running if child is still running, otherwise success
	return Status.RUNNING if result == Status.RUNNING else Status.SUCCESS

## Failure Node - always returns failure after child execution
class_name AlwaysFailNode extends DecoratorNode

## Always returns FAILURE regardless of child result.
## Useful for ensuring a branch always fails.

func _init(name: String = "AlwaysFail", child_node: BehaviorNode = null) -> void:
	super(name, child_node)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child and return failure
	
	if not child:
		return Status.FAILURE
	
	var result = child.execute(ctx)
	
	# Return running if child is still running, otherwise failure
	return Status.RUNNING if result == Status.RUNNING else Status.FAILURE
