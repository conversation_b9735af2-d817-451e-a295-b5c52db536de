class_name WeightedSelectorNode extends CompositeNode

## A selector that chooses children based on weighted probabilities
## Useful for creating varied AI behavior

var child_weights: Array[float] = []

func _init(name: String = "WeightedSelector", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)
	# Initialize weights to equal values
	child_weights.resize(children.size())
	for i in range(child_weights.size()):
		child_weights[i] = 1.0

func add_child_with_weight(child: BehaviorNode, weight: float) -> void:
	## Add a child with a specific weight
	add_child(child)
	child_weights.append(weight)

func set_child_weight(child_index: int, weight: float) -> void:
	## Set the weight of a specific child
	if child_index >= 0 and child_index < child_weights.size():
		child_weights[child_index] = weight

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute a randomly selected child based on weights
	
	if children.is_empty():
		return Status.FAILURE
	
	# Calculate total weight
	var total_weight = 0.0
	for weight in child_weights:
		total_weight += weight
	
	if total_weight <= 0.0:
		# If no valid weights, execute first child
		return children[0].execute(ctx)
	
	# Select child based on weighted random selection
	var random_value = randf() * total_weight
	var current_weight = 0.0
	
	for i in range(children.size()):
		current_weight += child_weights[i]
		if random_value <= current_weight:
			return children[i].execute(ctx)
	
	# Fallback to last child
	return children[children.size() - 1].execute(ctx)
