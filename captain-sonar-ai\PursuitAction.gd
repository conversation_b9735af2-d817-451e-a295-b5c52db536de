extends ActionNode
class_name PursuitAction

## Pursuit Action
##
## Action to pursue the enemy

func _init() -> void:
	super("Pursuit")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.is_enemy_detected():
		return Status.FAILURE
	
	# Calculate direction toward enemy
	var current_pos = ctx.submarine_position
	var enemy_pos = ctx.enemy_position
	
	var direction = ""
	var dx = enemy_pos.x - current_pos.x
	var dy = enemy_pos.y - current_pos.y
	
	# Choose direction that moves toward enemy
	if abs(dx) > abs(dy):
		direction = "EAST" if dx > 0 else "WEST"
	else:
		direction = "SOUTH" if dy > 0 else "NORTH"
	
	if debug_enabled:
		print("PursuitAction: Pursuing enemy ", direction, " toward ", enemy_pos)
	
	ctx.emit_event("movement_decision", {"direction": direction})
	return Status.SUCCESS
