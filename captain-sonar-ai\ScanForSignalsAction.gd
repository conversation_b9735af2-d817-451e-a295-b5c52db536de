extends ActionNode
class_name ScanForSignalsAction

## Scan For Signals Action
##
## Action to scan for enemy signals

func _init() -> void:
	super("ScanForSignals")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("ScanForSignalsAction: Scanning for signals")
	
	ctx.emit_event("scanning_for_signals", {
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
