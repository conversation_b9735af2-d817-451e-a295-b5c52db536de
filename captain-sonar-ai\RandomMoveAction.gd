extends ActionNode
class_name RandomMoveAction

## Random Move Action
##
## Action to move in a random valid direction

var valid_directions: Array[String] = ["NORTH", "SOUTH", "EAST", "WEST"]

func _init() -> void:
	super("RandomMove")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if valid_directions.is_empty():
		return Status.FAILURE
	
	var direction = valid_directions[randi() % valid_directions.size()]
	
	if debug_enabled:
		print("RandomMoveAction: Moving ", direction)
	
	ctx.emit_event("movement_decision", {"direction": direction})
	return Status.SUCCESS
