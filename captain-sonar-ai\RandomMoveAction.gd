class_name RandomMoveAction extends ActionNode

## Action to move in a random direction

func _init(name: String = "RandomMove") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute random movement

	# Generate random direction
	var directions = ["north", "south", "east", "west"]
	var random_direction = directions[randi() % directions.size()]

	# Prepare movement data
	var move_data = {
		"direction": random_direction,
		"current_position": ctx.submarine_position,
		"movement_type": "random"
	}

	# Emit movement command event
	ctx.emit_event("movement_command", move_data)

	return Status.SUCCESS
## Action to move in a random valid direction

var valid_directions: Array[String] = ["NORTH", "SOUTH", "EAST", "WEST"]

func _init() -> void:
	super("RandomMove")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if valid_directions.is_empty():
		return Status.FAILURE
	
	var direction = valid_directions[randi() % valid_directions.size()]
	
	if debug_enabled:
		print("RandomMoveAction: Moving ", direction)
	
	ctx.emit_event("movement_decision", {"direction": direction})
	return Status.SUCCESS
