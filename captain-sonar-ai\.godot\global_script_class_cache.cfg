list=[{
"base": &"Node",
"class": &"AIAdvancedCoordination",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIAdvancedCoordination.gd"
}, {
"base": &"Node",
"class": &"AIBehaviorManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIBehaviorManager.gd"
}, {
"base": &"AIStation",
"class": &"AICaptain",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AICaptain.gd"
}, {
"base": &"Node",
"class": &"AIController",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIController.gd"
}, {
"base": &"Node",
"class": &"AIEngineer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIEngineer.gd"
}, {
"base": &"Node",
"class": &"AIEventBus",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIEventBus.gd"
}, {
"base": &"Node",
"class": &"AIFirstMate",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIFirstMate.gd"
}, {
"base": &"Node",
"class": &"AIHardModeManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIHardModeManager.gd"
}, {
"base": &"Node",
"class": &"AIManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIManager.gd"
}, {
"base": &"Node",
"class": &"AIPatternAnalyzer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIPatternAnalyzer.gd"
}, {
"base": &"Node",
"class": &"AIPerformanceMonitor",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIPerformanceMonitor.gd"
}, {
"base": &"Resource",
"class": &"AIPersonality",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIPersonality.gd"
}, {
"base": &"Node",
"class": &"AIPlayerModeler",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIPlayerModeler.gd"
}, {
"base": &"Resource",
"class": &"AIProfile",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIProfile.gd"
}, {
"base": &"AIStation",
"class": &"AIRadioOperator",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIRadioOperator.gd"
}, {
"base": &"Node",
"class": &"AIStation",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIStation.gd"
}, {
"base": &"RefCounted",
"class": &"AIStrategicPlanner",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIStrategicPlanner.gd"
}, {
"base": &"Node",
"class": &"AITiming",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AITiming.gd"
}, {
"base": &"LeafNode",
"class": &"ActionNode",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ActionNode.gd"
}, {
"base": &"ActionNode",
"class": &"AnalyzeIntelligenceAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AnalyzeIntelligenceAction.gd"
}, {
"base": &"ActionNode",
"class": &"AttackAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AttackAction.gd"
}, {
"base": &"RefCounted",
"class": &"BehaviorNode",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://BehaviorNode.gd"
}, {
"base": &"ActionNode",
"class": &"ChargeEquipmentAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ChargeEquipmentAction.gd"
}, {
"base": &"LeafNode",
"class": &"ConditionNode",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ConditionNode.gd"
}, {
"base": &"ActionNode",
"class": &"CoordinateWithStationsAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://CoordinateWithStationsAction.gd"
}, {
"base": &"ActionNode",
"class": &"DefensivePositionAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://DefensivePositionAction.gd"
}, {
"base": &"ConditionNode",
"class": &"EnemyCloseCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://EnemyCloseCondition.gd"
}, {
"base": &"ConditionNode",
"class": &"EnemyDetectedCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://EnemyDetectedCondition.gd"
}, {
"base": &"ConditionNode",
"class": &"EquipmentReadyCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://EquipmentReadyCondition.gd"
}, {
"base": &"ActionNode",
"class": &"EvadeAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://EvadeAction.gd"
}, {
"base": &"ActionNode",
"class": &"GenerateIntelligenceReportAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://GenerateIntelligenceReportAction.gd"
}, {
"base": &"ConditionNode",
"class": &"HasCommunicationRequestCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://HasCommunicationRequestCondition.gd"
}, {
"base": &"ConditionNode",
"class": &"HasTrackingDataCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://HasTrackingDataCondition.gd"
}, {
"base": &"ConditionNode",
"class": &"HealthCriticalCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://HealthCriticalCondition.gd"
}, {
"base": &"ActionNode",
"class": &"HistoricalAnalysisAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://HistoricalAnalysisAction.gd"
}, {
"base": &"",
"class": &"InverterNode",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://BehaviorDecorators.gd"
}, {
"base": &"ActionNode",
"class": &"ListenForEnemyAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ListenForEnemyAction.gd"
}, {
"base": &"ActionNode",
"class": &"MaintainEquipmentAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://MaintainEquipmentAction.gd"
}, {
"base": &"ActionNode",
"class": &"MonitorCommunicationsAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://MonitorCommunicationsAction.gd"
}, {
"base": &"ActionNode",
"class": &"MoveAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://MoveAction.gd"
}, {
"base": &"ActionNode",
"class": &"PatrolAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://PatrolAction.gd"
}, {
"base": &"ActionNode",
"class": &"PatternAnalysisAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://PatternAnalysisAction.gd"
}, {
"base": &"ActionNode",
"class": &"PredictEnemyMovementAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://PredictEnemyMovementAction.gd"
}, {
"base": &"ActionNode",
"class": &"ProcessCommunicationRequestAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ProcessCommunicationRequestAction.gd"
}, {
"base": &"ActionNode",
"class": &"PursuitAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://PursuitAction.gd"
}, {
"base": &"ActionNode",
"class": &"RandomMoveAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://RandomMoveAction.gd"
}, {
"base": &"ActionNode",
"class": &"RepairSystemAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://RepairSystemAction.gd"
}, {
"base": &"ActionNode",
"class": &"ReviewDataAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ReviewDataAction.gd"
}, {
"base": &"ActionNode",
"class": &"ScanForSignalsAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://ScanForSignalsAction.gd"
}, {
"base": &"",
"class": &"SequenceNode",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://BehaviorComposites.gd"
}, {
"base": &"Resource",
"class": &"SubmarineState",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://SubmarineState.gd"
}, {
"base": &"ActionNode",
"class": &"SurfaceAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://SurfaceAction.gd"
}, {
"base": &"ConditionNode",
"class": &"SurfacedCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://SurfacedCondition.gd"
}, {
"base": &"ConditionNode",
"class": &"SystemDamagedCondition",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://SystemDamagedCondition.gd"
}, {
"base": &"ActionNode",
"class": &"TrackEnemyAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://TrackEnemyAction.gd"
}, {
"base": &"ActionNode",
"class": &"TrendAnalysisAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://TrendAnalysisAction.gd"
}, {
"base": &"ActionNode",
"class": &"UpdatePredictionAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://UpdatePredictionAction.gd"
}, {
"base": &"ActionNode",
"class": &"UpdateTrackingDataAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://UpdateTrackingDataAction.gd"
}, {
"base": &"ActionNode",
"class": &"UseEquipmentAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://UseEquipmentAction.gd"
}, {
"base": &"ActionNode",
"class": &"WaitAction",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://WaitAction.gd"
}, {
"base": &"Node",
"class": &"WeaponSystems",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://WeaponSystems.gd"
}]
