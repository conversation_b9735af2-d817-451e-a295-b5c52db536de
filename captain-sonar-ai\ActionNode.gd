extends LeafNode
class_name ActionNode

## Base Action Node
##
## Base class for all action nodes that perform actual work in behavior trees.
## Actions represent concrete operations like moving, attacking, or repairing.

func _init(name: String = "Action") -> void:
	super(name)

## Override this method in subclasses to implement the action
func _execute_implementation(ctx: BehaviorContext) -> Status:
	push_warning("ActionNode: _execute_implementation not implemented in " + node_name)
	return Status.FAILURE
