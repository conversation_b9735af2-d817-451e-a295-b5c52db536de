class_name BehaviorContext extends RefCounted

## Context object that provides behavior nodes access to game state and AI systems
## This allows nodes to make decisions based on current game conditions

## References to AI systems
var ai_controller: AIController
var ai_event_bus: AIEventBus
var ai_personality: AIPersonality
var station_owner: Node  # The AI station that owns this behavior tree

## Game state information
var submarine_position: Vector2i
var submarine_health: int
var is_surfaced: bool
var current_turn: int

## Shared data between nodes in the same tree
var blackboard: Dictionary = {}

## Enemy intelligence
var enemy_position: Vector2i
var enemy_certainty: float = 0.0
var enemy_last_known_position: Vector2i

## Equipment status
var equipment_status: Dictionary = {}

## System status
var systems_damaged: Array[String] = []

func _init(controller: <PERSON><PERSON><PERSON>roller, owner: Node) -> void:
	ai_controller = controller
	station_owner = owner
	
	if controller:
		ai_event_bus = controller.ai_event_bus
		ai_personality = controller.ai_personality

func update_game_state(position: Vector2i, health: int, surfaced: bool, turn: int) -> void:
	## Update the current game state information
	
	submarine_position = position
	submarine_health = health
	is_surfaced = surfaced
	current_turn = turn

func update_enemy_intelligence(position: Vector2i, certainty: float) -> void:
	## Update enemy position intelligence
	
	if certainty > enemy_certainty:
		enemy_position = position
		enemy_certainty = certainty
		enemy_last_known_position = position

func update_equipment_status(status: Dictionary) -> void:
	## Update equipment status information
	equipment_status = status

func update_system_status(damaged_systems: Array[String]) -> void:
	## Update system damage status
	systems_damaged = damaged_systems

func get_blackboard_value(key: String, default_value = null):
	## Get a value from the shared blackboard
	return blackboard.get(key, default_value)

func set_blackboard_value(key: String, value) -> void:
	## Set a value in the shared blackboard
	blackboard[key] = value

func clear_blackboard() -> void:
	## Clear all blackboard data
	blackboard.clear()

func is_enemy_detected() -> bool:
	## Check if we have recent enemy intelligence
	return enemy_certainty > 0.3

func is_enemy_close() -> bool:
	## Check if enemy is close to our position
	if not is_enemy_detected():
		return false
	
	var distance = submarine_position.distance_to(enemy_position)
	return distance <= 3.0

func is_health_critical() -> bool:
	## Check if submarine health is critically low
	return submarine_health <= 1

func is_system_damaged(system_name: String) -> bool:
	## Check if a specific system is damaged
	return system_name in systems_damaged

func can_use_equipment(equipment_name: String) -> bool:
	## Check if specific equipment is ready to use
	if not equipment_status.has(equipment_name):
		return false
	
	var status = equipment_status[equipment_name]
	return status.get("ready", false)

func get_personality_parameter(parameter_name: String) -> float:
	## Get a personality parameter value
	if not ai_personality:
		return 0.5  # Default value
	
	match parameter_name:
		"reaction_time":
			return ai_personality.reaction_time
		"accuracy":
			return ai_personality.accuracy
		"aggression":
			return ai_personality.aggression
		"caution":
			return ai_personality.caution
		"cooperation":
			return ai_personality.cooperation_level
		_:
			return 0.5

func should_make_optimal_decision() -> bool:
	## Check if AI should make optimal decision based on accuracy
	if ai_personality:
		return ai_personality.should_make_optimal_decision()
	return randf() < 0.5

func emit_event(event_type: String, data: Dictionary = {}) -> void:
	## Emit an event through the event bus
	if ai_event_bus:
		ai_event_bus.emit_event(event_type, data)
