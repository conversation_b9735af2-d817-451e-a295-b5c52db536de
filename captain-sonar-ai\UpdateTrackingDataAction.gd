class_name UpdateTrackingDataAction extends ActionNode

## Action to update enemy tracking data

func _init(name: String = "UpdateTrackingData") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Update tracking data with latest information

	# Prepare tracking update data
	var update_data = {
		"position": ctx.enemy_position,
		"certainty": ctx.enemy_certainty,
		"timestamp": Time.get_unix_time_from_system()
	}

	# Emit tracking update event
	ctx.emit_event("tracking_data_updated", update_data)

	return Status.SUCCESS
## Action to update enemy tracking data

func _init() -> void:
	super("UpdateTrackingData")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("UpdateTrackingDataAction: Updating tracking data")
	
	ctx.emit_event("tracking_data_updated", {
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
