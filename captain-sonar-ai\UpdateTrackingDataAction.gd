extends ActionNode
class_name UpdateTrackingDataAction

## Update Tracking Data Action
##
## Action to update enemy tracking data

func _init() -> void:
	super("UpdateTrackingData")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("UpdateTrackingDataAction: Updating tracking data")
	
	ctx.emit_event("tracking_data_updated", {
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
