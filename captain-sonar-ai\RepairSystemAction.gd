extends ActionNode
class_name RepairSystemAction

## Repair System Action
##
## Action to repair a damaged system

var system_name: String = ""

func _init(system: String) -> void:
	super("RepairSystem_" + system)
	system_name = system

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.is_system_damaged(system_name):
		return Status.SUCCESS  # Already repaired
	
	if debug_enabled:
		print("RepairSystemAction: Repairing ", system_name)
	
	ctx.emit_event("system_repair", {"system": system_name})
	return Status.SUCCESS
