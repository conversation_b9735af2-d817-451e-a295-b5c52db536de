extends ActionNode
class_name WaitAction

## Wait Action
##
## Action to wait for a specified duration

var wait_duration: float = 1.0
var start_time: float = 0.0
var is_waiting: bool = false

func _init(duration: float = 1.0) -> void:
	super("Wait")
	wait_duration = duration

func _execute_implementation(ctx: BehaviorContext) -> Status:
	var current_time = Time.get_unix_time_from_system()
	
	if not is_waiting:
		start_time = current_time
		is_waiting = true
		
		if debug_enabled:
			print("WaitAction: Starting wait for ", wait_duration, " seconds")
		
		return Status.RUNNING
	
	if current_time - start_time >= wait_duration:
		is_waiting = false
		
		if debug_enabled:
			print("WaitAction: Wait completed")
		
		return Status.SUCCESS
	
	return Status.RUNNING

func reset() -> void:
	super.reset()
	is_waiting = false
	start_time = 0.0
