extends ActionNode
class_name MonitorCommunicationsAction

## Monitor Communications Action
##
## Action to monitor communications

func _init() -> void:
	super("MonitorCommunications")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("MonitorCommunicationsAction: Monitoring communications")
	
	ctx.emit_event("monitoring_communications", {
		"timestamp": Time.get_unix_time_from_system()
	})
	return Status.SUCCESS
