class_name SelectorNode extends Composite<PERSON><PERSON>

## Executes children in order until one succeeds. Returns FAILURE only if all children fail.
## Returns SUCCESS as soon as any child succeeds.
## Returns RUNNING if any child is still running.

func _init(name: String = "Selector", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in sequence until one succeeds or all fail
	
	# Start from current child index (for RUNNING state continuity)
	for i in range(current_child_index, children.size()):
		var child = children[i]
		current_child_index = i
		
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				# Child succeeded, selector succeeds
				current_child_index = 0  # Reset for next execution
				return Status.SUCCESS
			Status.RUNNING:
				# Child is still running, selector is running
				return Status.RUNNING
			Status.FAILURE:
				# Child failed, try next child
				continue
	
	# All children failed
	current_child_index = 0  # Reset for next execution
	return Status.FAILURE
