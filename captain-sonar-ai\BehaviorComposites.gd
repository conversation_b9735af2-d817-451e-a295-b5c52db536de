extends RefCounted

## Composite Behavior Tree Nodes
##
## This file contains the implementation of composite nodes that control
## the flow of execution through multiple child nodes. These replace the
## complex if-else chains in the old AI implementation.

## Sequence Node - executes children in order until one fails
class_name SequenceNode extends CompositeNode

## Executes all children in sequence. Returns SUCCESS only if all children succeed.
## Returns FAILURE as soon as any child fails.
## Returns RUNNING if any child is still running.

func _init(name: String = "Sequence", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in sequence until one fails or all succeed
	
	# Start from current child index (for RUNNING state continuity)
	for i in range(current_child_index, children.size()):
		var child = children[i]
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				# Continue to next child
				current_child_index = i + 1
				continue
			Status.FAILURE:
				# Sequence fails if any child fails
				current_child_index = 0  # Reset for next execution
				return Status.FAILURE
			Status.RUNNING:
				# Keep current position and return running
				current_child_index = i
				return Status.RUNNING
	
	# All children succeeded
	current_child_index = 0  # Reset for next execution
	return Status.SUCCESS

## Selector Node - executes children until one succeeds
class_name SelectorNode extends CompositeNode

## Executes children in order until one succeeds. Returns FAILURE only if all children fail.
## Returns SUCCESS as soon as any child succeeds.
## Returns RUNNING if any child is still running.

func _init(name: String = "Selector", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in sequence until one succeeds or all fail
	
	# Start from current child index (for RUNNING state continuity)
	for i in range(current_child_index, children.size()):
		var child = children[i]
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				# Selector succeeds if any child succeeds
				current_child_index = 0  # Reset for next execution
				return Status.SUCCESS
			Status.FAILURE:
				# Continue to next child
				current_child_index = i + 1
				continue
			Status.RUNNING:
				# Keep current position and return running
				current_child_index = i
				return Status.RUNNING
	
	# All children failed
	current_child_index = 0  # Reset for next execution
	return Status.FAILURE

## Parallel Node - executes all children simultaneously
class_name ParallelNode extends CompositeNode

## Executes all children in parallel. Success/failure policy can be configured.

enum Policy {
	REQUIRE_ALL,    # All children must succeed for success
	REQUIRE_ONE,    # At least one child must succeed for success
	REQUIRE_MAJORITY # Majority of children must succeed for success
}

var success_policy: Policy = Policy.REQUIRE_ALL
var failure_policy: Policy = Policy.REQUIRE_ONE

## Track child execution states
var child_states: Array[Status] = []

func _init(name: String = "Parallel", child_nodes: Array[BehaviorNode] = [], 
		   success_pol: Policy = Policy.REQUIRE_ALL, failure_pol: Policy = Policy.REQUIRE_ONE) -> void:
	super(name, child_nodes)
	success_policy = success_pol
	failure_policy = failure_pol
	_initialize_child_states()

func _initialize_child_states() -> void:
	## Initialize child state tracking
	child_states.clear()
	for i in children.size():
		child_states.append(Status.RUNNING)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute all children in parallel and evaluate based on policies
	
	var success_count = 0
	var failure_count = 0
	var running_count = 0
	
	# Execute all children and track their states
	for i in range(children.size()):
		var child = children[i]
		
		# Only execute if not already completed
		if child_states[i] == Status.RUNNING:
			child_states[i] = child.execute(ctx)
		
		# Count current states
		match child_states[i]:
			Status.SUCCESS:
				success_count += 1
			Status.FAILURE:
				failure_count += 1
			Status.RUNNING:
				running_count += 1
	
	# Evaluate success condition
	var should_succeed = false
	match success_policy:
		Policy.REQUIRE_ALL:
			should_succeed = success_count == children.size()
		Policy.REQUIRE_ONE:
			should_succeed = success_count >= 1
		Policy.REQUIRE_MAJORITY:
			should_succeed = success_count > children.size() / 2
	
	# Evaluate failure condition
	var should_fail = false
	match failure_policy:
		Policy.REQUIRE_ALL:
			should_fail = failure_count == children.size()
		Policy.REQUIRE_ONE:
			should_fail = failure_count >= 1
		Policy.REQUIRE_MAJORITY:
			should_fail = failure_count > children.size() / 2
	
	# Return result based on policies
	if should_succeed:
		_reset_child_states()
		return Status.SUCCESS
	elif should_fail:
		_reset_child_states()
		return Status.FAILURE
	else:
		# Still running
		return Status.RUNNING

func reset() -> void:
	## Reset parallel node and all children
	super.reset()
	_reset_child_states()

func _reset_child_states() -> void:
	## Reset all child states to running
	for i in range(child_states.size()):
		child_states[i] = Status.RUNNING

func add_child(child: BehaviorNode) -> void:
	## Override to maintain child state tracking
	super.add_child(child)
	child_states.append(Status.RUNNING)

func remove_child(child: BehaviorNode) -> void:
	## Override to maintain child state tracking
	var index = children.find(child)
	if index >= 0:
		super.remove_child(child)
		child_states.remove_at(index)

## Priority Selector Node - executes children based on priority
class_name PrioritySelectorNode extends CompositeNode

## Like a selector, but children have priorities and are executed in priority order.
## Higher priority children are checked first.

var child_priorities: Array[int] = []
var sorted_indices: Array[int] = []

func _init(name: String = "PrioritySelector", child_nodes: Array[BehaviorNode] = [], 
		   priorities: Array[int] = []) -> void:
	super(name, child_nodes)
	child_priorities = priorities
	_update_sorted_indices()

func add_child_with_priority(child: BehaviorNode, priority: int) -> void:
	## Add a child with a specific priority
	add_child(child)
	child_priorities.append(priority)
	_update_sorted_indices()

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in priority order until one succeeds
	
	for i in sorted_indices:
		if i >= children.size():
			continue
			
		var child = children[i]
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				return Status.SUCCESS
			Status.RUNNING:
				return Status.RUNNING
			Status.FAILURE:
				continue  # Try next priority child
	
	# All children failed
	return Status.FAILURE

func _update_sorted_indices() -> void:
	## Update the sorted indices based on priorities
	sorted_indices.clear()
	
	# Create array of indices
	for i in range(children.size()):
		sorted_indices.append(i)
	
	# Sort indices by priority (higher priority first)
	sorted_indices.sort_custom(func(a, b): 
		var priority_a = child_priorities[a] if a < child_priorities.size() else 0
		var priority_b = child_priorities[b] if b < child_priorities.size() else 0
		return priority_a > priority_b
	)

## Random Selector Node - executes children in random order
class_name RandomSelectorNode extends CompositeNode

## Like a selector, but children are tried in random order.
## Useful for adding unpredictability to AI behavior.

var shuffled_indices: Array[int] = []
var current_shuffle_index: int = 0

func _init(name: String = "RandomSelector", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)
	_shuffle_children()

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in random order until one succeeds
	
	# Reshuffle if we're starting fresh
	if current_shuffle_index == 0:
		_shuffle_children()
	
	# Try children in shuffled order
	for i in range(current_shuffle_index, shuffled_indices.size()):
		var child_index = shuffled_indices[i]
		if child_index >= children.size():
			continue
			
		var child = children[child_index]
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				current_shuffle_index = 0  # Reset for next execution
				return Status.SUCCESS
			Status.RUNNING:
				current_shuffle_index = i  # Remember position
				return Status.RUNNING
			Status.FAILURE:
				current_shuffle_index = i + 1
				continue
	
	# All children failed
	current_shuffle_index = 0  # Reset for next execution
	return Status.FAILURE

func _shuffle_children() -> void:
	## Create a shuffled array of child indices
	shuffled_indices.clear()
	
	for i in range(children.size()):
		shuffled_indices.append(i)
	
	# Fisher-Yates shuffle
	for i in range(shuffled_indices.size() - 1, 0, -1):
		var j = randi() % (i + 1)
		var temp = shuffled_indices[i]
		shuffled_indices[i] = shuffled_indices[j]
		shuffled_indices[j] = temp

func reset() -> void:
	## Reset and reshuffle
	super.reset()
	current_shuffle_index = 0
	_shuffle_children()

## Weighted Selector Node - executes children based on weights
class_name WeightedSelectorNode extends CompositeNode

## Selects a child to execute based on weighted probabilities.
## Useful for creating varied AI behavior patterns.

var child_weights: Array[float] = []
var selected_child_index: int = -1

func _init(name: String = "WeightedSelector", child_nodes: Array[BehaviorNode] = [], 
		   weights: Array[float] = []) -> void:
	super(name, child_nodes)
	child_weights = weights
	_normalize_weights()

func add_child_with_weight(child: BehaviorNode, weight: float) -> void:
	## Add a child with a specific weight
	add_child(child)
	child_weights.append(weight)
	_normalize_weights()

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Select and execute a child based on weights
	
	# Select child if not already selected
	if selected_child_index == -1:
		selected_child_index = _select_weighted_child()
	
	if selected_child_index >= 0 and selected_child_index < children.size():
		var result = children[selected_child_index].execute(ctx)
		
		# Reset selection when child completes
		if result != Status.RUNNING:
			selected_child_index = -1
		
		return result
	
	return Status.FAILURE

func _select_weighted_child() -> int:
	## Select a child index based on weights
	
	if child_weights.is_empty():
		return randi() % children.size() if not children.is_empty() else -1
	
	var total_weight = 0.0
	for weight in child_weights:
		total_weight += weight
	
	if total_weight <= 0.0:
		return randi() % children.size() if not children.is_empty() else -1
	
	var random_value = randf() * total_weight
	var cumulative_weight = 0.0
	
	for i in range(child_weights.size()):
		cumulative_weight += child_weights[i]
		if random_value <= cumulative_weight:
			return i
	
	return children.size() - 1  # Fallback to last child

func _normalize_weights() -> void:
	## Ensure weights are valid (no negative values)
	for i in range(child_weights.size()):
		child_weights[i] = max(0.0, child_weights[i])

func reset() -> void:
	## Reset weighted selector
	super.reset()
	selected_child_index = -1
