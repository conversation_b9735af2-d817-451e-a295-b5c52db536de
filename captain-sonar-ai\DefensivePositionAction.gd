extends ActionNode
class_name DefensivePositionAction

## Defensive Position Action
##
## Action to take defensive position

func _init() -> void:
	super("DefensivePosition")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	# For now, implement as maintaining distance from enemy
	if ctx.is_enemy_detected() and ctx.is_enemy_close():
		# Move away from enemy
		var evade_action = EvadeAction.new()
		return evade_action.execute(ctx)
	else:
		# Random defensive movement
		var random_action = RandomMoveAction.new()
		return random_action.execute(ctx)
