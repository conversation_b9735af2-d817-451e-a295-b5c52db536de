class_name <PERSON>llelNode extends CompositeNode

## Executes all children simultaneously. Success/failure policy can be configured.

enum Policy {
	REQUIRE_ONE,    # Succeed if at least one child succeeds
	REQUIRE_ALL,    # Succeed only if all children succeed
	REQUIRE_MAJORITY # Succeed if majority of children succeed
}

var success_policy: Policy = Policy.REQUIRE_ONE
var failure_policy: Policy = Policy.REQUIRE_ALL

func _init(name: String = "Parallel", child_nodes: Array[BehaviorNode] = [], success_pol: Policy = Policy.REQUIRE_ONE, failure_pol: Policy = Policy.REQUIRE_ALL) -> void:
	super(name, child_nodes)
	success_policy = success_pol
	failure_policy = failure_pol

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute all children in parallel
	
	var success_count = 0
	var failure_count = 0
	var running_count = 0
	
	# Execute all children
	for child in children:
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				success_count += 1
			Status.FAILURE:
				failure_count += 1
			Status.RUNNING:
				running_count += 1
	
	# Check failure conditions first
	var should_fail = false
	match failure_policy:
		Policy.REQUIRE_ONE:
			should_fail = failure_count >= 1
		Policy.REQUIRE_ALL:
			should_fail = failure_count == children.size()
		Policy.REQUIRE_MAJORITY:
			should_fail = failure_count > children.size() / 2
	
	if should_fail:
		return Status.FAILURE
	
	# Check success conditions
	var should_succeed = false
	match success_policy:
		Policy.REQUIRE_ONE:
			should_succeed = success_count >= 1
		Policy.REQUIRE_ALL:
			should_succeed = success_count == children.size()
		Policy.REQUIRE_MAJORITY:
			should_succeed = success_count > children.size() / 2
	
	if should_succeed:
		return Status.SUCCESS
	
	# If we have running children and haven't failed/succeeded, keep running
	if running_count > 0:
		return Status.RUNNING
	
	# Default to failure if no clear result
	return Status.FAILURE
