extends Node
class_name AIPatternAnalyzer

## AI Pattern Analysis System
##
## This system analyzes player behavior patterns to predict future actions
## and identify exploitable weaknesses. Used by hard mode AI for advanced
## tactical decision making.

signal pattern_detected(pattern: Dictionary)
signal weakness_identified(weakness: Dictionary)

# Pattern tracking
var movement_patterns: Array[Dictionary] = []
var action_patterns: Array[Dictionary] = []
var timing_patterns: Array[Dictionary] = []
var strategic_patterns: Array[Dictionary] = []

# Analysis state
var turn_history: Array[Dictionary] = []
var pattern_confidence: Dictionary = {}
var identified_weaknesses: Array[Dictionary] = []

# Configuration
var max_history_size: int = 50
var min_pattern_occurrences: int = 3
var confidence_threshold: float = 0.7

# Performance metrics
var patterns_detected: int = 0
var successful_predictions: int = 0
var total_predictions: int = 0

func _init():
	name = "AIPatternAnalyzer"

func analyze_turn(game_state: Dictionary) -> void:
	## Analyze the current turn for patterns
	
	# Add to turn history
	var turn_data = _extract_turn_data(game_state)
	turn_history.append(turn_data)
	
	# Maintain history size
	if turn_history.size() > max_history_size:
		turn_history.pop_front()
	
	# Analyze different pattern types
	_analyze_movement_patterns()
	_analyze_action_patterns()
	_analyze_timing_patterns()
	_analyze_strategic_patterns()
	
	# Update pattern confidence
	_update_pattern_confidence()
	
	# Identify new weaknesses
	_identify_weaknesses()

func get_insights_for_station(station_name: String) -> Dictionary:
	## Get pattern insights relevant to a specific station
	
	var insights = {}
	
	match station_name:
		"captain":
			insights = _get_captain_insights()
		"engineer":
			insights = _get_engineer_insights()
		"firstmate":
			insights = _get_firstmate_insights()
		"radio_operator":
			insights = _get_radio_insights()
	
	return insights

func enhance_prediction(prediction: Dictionary) -> Dictionary:
	## Enhance a prediction with pattern analysis
	
	var enhancement = {}
	
	# Add movement pattern predictions
	if movement_patterns.size() > 0:
		var likely_movement = _predict_next_movement()
		enhancement["predicted_movement"] = likely_movement
	
	# Add action pattern predictions
	if action_patterns.size() > 0:
		var likely_actions = _predict_next_actions()
		enhancement["predicted_actions"] = likely_actions
	
	# Add timing predictions
	if timing_patterns.size() > 0:
		var timing_prediction = _predict_timing()
		enhancement["predicted_timing"] = timing_prediction
	
	return enhancement

func identify_weaknesses() -> Array[Dictionary]:
	## Identify exploitable weaknesses in player behavior
	
	return identified_weaknesses.duplicate()

func update_from_validation(predicted: Dictionary, actual: Dictionary, accuracy: float) -> void:
	## Update pattern analysis based on prediction validation
	
	total_predictions += 1
	
	if accuracy > confidence_threshold:
		successful_predictions += 1
		
		# Strengthen patterns that led to successful predictions
		_strengthen_successful_patterns(predicted, actual)
	else:
		# Weaken patterns that led to failed predictions
		_weaken_failed_patterns(predicted, actual)

func get_status() -> Dictionary:
	## Get pattern analyzer status
	
	return {
		"patterns_detected": patterns_detected,
		"movement_patterns": movement_patterns.size(),
		"action_patterns": action_patterns.size(),
		"timing_patterns": timing_patterns.size(),
		"strategic_patterns": strategic_patterns.size(),
		"identified_weaknesses": identified_weaknesses.size(),
		"prediction_accuracy": _get_prediction_accuracy(),
		"pattern_confidence": pattern_confidence
	}

func _extract_turn_data(game_state: Dictionary) -> Dictionary:
	## Extract relevant data from game state for pattern analysis
	
	return {
		"turn": game_state.get("turn_count", 0),
		"timestamp": Time.get_unix_time_from_system(),
		"player_position": game_state.get("enemy_position", Vector2i.ZERO),
		"player_health": game_state.get("enemy_health", 4),
		"our_position": game_state.get("our_position", Vector2i.ZERO),
		"our_health": game_state.get("our_health", 4),
		"actions_taken": game_state.get("enemy_actions", []),
		"systems_used": game_state.get("enemy_systems_used", []),
		"movement_direction": game_state.get("enemy_movement", ""),
		"response_time": game_state.get("enemy_response_time", 0.0),
		"surfaced": game_state.get("enemy_surfaced", false)
	}

func _analyze_movement_patterns() -> void:
	## Analyze player movement patterns
	
	if turn_history.size() < 5:
		return
	
	# Look for repeating movement sequences
	var recent_movements = []
	for i in range(max(0, turn_history.size() - 10), turn_history.size()):
		var turn = turn_history[i]
		if turn.movement_direction != "":
			recent_movements.append(turn.movement_direction)
	
	# Detect common sequences
	var sequences = _find_movement_sequences(recent_movements)
	
	for sequence in sequences:
		if sequence.occurrences >= min_pattern_occurrences:
			var pattern = {
				"type": "movement_sequence",
				"sequence": sequence.moves,
				"confidence": float(sequence.occurrences) / float(recent_movements.size()),
				"detected_at": Time.get_unix_time_from_system()
			}
			
			if not _pattern_exists(movement_patterns, pattern):
				movement_patterns.append(pattern)
				patterns_detected += 1
				pattern_detected.emit(pattern)

func _analyze_action_patterns() -> void:
	## Analyze player action patterns
	
	if turn_history.size() < 3:
		return
	
	# Analyze action timing and sequences
	var action_sequences = []
	
	for i in range(turn_history.size() - 2):
		var sequence = []
		for j in range(3):  # Look at 3-turn sequences
			if i + j < turn_history.size():
				sequence.append(turn_history[i + j].actions_taken)
		
		if sequence.size() == 3:
			action_sequences.append(sequence)
	
	# Find common action patterns
	var common_patterns = _find_common_action_patterns(action_sequences)
	
	for pattern_data in common_patterns:
		if pattern_data.frequency >= min_pattern_occurrences:
			var pattern = {
				"type": "action_sequence",
				"pattern": pattern_data.pattern,
				"confidence": pattern_data.frequency / float(action_sequences.size()),
				"detected_at": Time.get_unix_time_from_system()
			}
			
			if not _pattern_exists(action_patterns, pattern):
				action_patterns.append(pattern)
				patterns_detected += 1
				pattern_detected.emit(pattern)

func _analyze_timing_patterns() -> void:
	## Analyze player timing patterns
	
	if turn_history.size() < 5:
		return
	
	# Analyze response time patterns
	var response_times = []
	for turn in turn_history:
		if turn.response_time > 0:
			response_times.append(turn.response_time)
	
	if response_times.size() >= 5:
		var avg_response = _calculate_average(response_times)
		var variance = _calculate_variance(response_times, avg_response)
		
		var timing_pattern = {
			"type": "response_timing",
			"average_response": avg_response,
			"variance": variance,
			"consistency": 1.0 - (variance / max(avg_response, 1.0)),
			"detected_at": Time.get_unix_time_from_system()
		}
		
		# Update or add timing pattern
		var existing_index = -1
		for i in range(timing_patterns.size()):
			if timing_patterns[i].type == "response_timing":
				existing_index = i
				break
		
		if existing_index >= 0:
			timing_patterns[existing_index] = timing_pattern
		else:
			timing_patterns.append(timing_pattern)
			patterns_detected += 1
			pattern_detected.emit(timing_pattern)

func _analyze_strategic_patterns() -> void:
	## Analyze high-level strategic patterns
	
	if turn_history.size() < 10:
		return
	
	# Analyze strategic preferences
	var strategic_data = {
		"aggressive_moves": 0,
		"defensive_moves": 0,
		"surface_frequency": 0,
		"weapon_usage": 0
	}
	
	for turn in turn_history:
		# Count aggressive vs defensive behavior
		if turn.actions_taken.has("torpedo") or turn.actions_taken.has("mine"):
			strategic_data.aggressive_moves += 1
		
		if turn.surfaced:
			strategic_data.surface_frequency += 1
		
		if turn.systems_used.size() > 2:
			strategic_data.weapon_usage += 1
	
	# Create strategic pattern
	var total_turns = turn_history.size()
	var strategic_pattern = {
		"type": "strategic_preference",
		"aggression_ratio": float(strategic_data.aggressive_moves) / float(total_turns),
		"surface_ratio": float(strategic_data.surface_frequency) / float(total_turns),
		"weapon_usage_ratio": float(strategic_data.weapon_usage) / float(total_turns),
		"detected_at": Time.get_unix_time_from_system()
	}
	
	# Update or add strategic pattern
	var existing_index = -1
	for i in range(strategic_patterns.size()):
		if strategic_patterns[i].type == "strategic_preference":
			existing_index = i
			break
	
	if existing_index >= 0:
		strategic_patterns[existing_index] = strategic_pattern
	else:
		strategic_patterns.append(strategic_pattern)
		patterns_detected += 1
		pattern_detected.emit(strategic_pattern)

func _identify_weaknesses() -> void:
	## Identify exploitable weaknesses based on patterns
	
	identified_weaknesses.clear()
	
	# Movement predictability weakness
	for pattern in movement_patterns:
		if pattern.confidence > 0.8:
			identified_weaknesses.append({
				"type": "predictable_movement",
				"description": "Player follows predictable movement patterns",
				"confidence": pattern.confidence,
				"exploitation": "Anticipate movement and position for intercept"
			})
	
	# Timing weakness
	for pattern in timing_patterns:
		if pattern.type == "response_timing" and pattern.consistency > 0.7:
			identified_weaknesses.append({
				"type": "consistent_timing",
				"description": "Player has consistent response timing",
				"confidence": pattern.consistency,
				"exploitation": "Time actions to exploit response delays"
			})
	
	# Strategic weakness
	for pattern in strategic_patterns:
		if pattern.type == "strategic_preference":
			if pattern.aggression_ratio > 0.8:
				identified_weaknesses.append({
					"type": "overly_aggressive",
					"description": "Player tends to be overly aggressive",
					"confidence": pattern.aggression_ratio,
					"exploitation": "Use defensive positioning to counter aggression"
				})
			elif pattern.aggression_ratio < 0.2:
				identified_weaknesses.append({
					"type": "overly_defensive",
					"description": "Player tends to be overly defensive",
					"confidence": 1.0 - pattern.aggression_ratio,
					"exploitation": "Apply pressure to force mistakes"
				})

func _find_movement_sequences(movements: Array) -> Array:
	## Find repeating movement sequences
	
	var sequences = []
	var sequence_counts = {}
	
	# Look for sequences of length 2-4
	for seq_length in range(2, 5):
		for i in range(movements.size() - seq_length + 1):
			var sequence = []
			for j in range(seq_length):
				sequence.append(movements[i + j])
			
			var seq_key = str(sequence)
			if not sequence_counts.has(seq_key):
				sequence_counts[seq_key] = {"moves": sequence, "occurrences": 0}
			sequence_counts[seq_key].occurrences += 1
	
	# Convert to array
	for seq_key in sequence_counts:
		sequences.append(sequence_counts[seq_key])
	
	return sequences

func _find_common_action_patterns(sequences: Array) -> Array:
	## Find common action patterns in sequences
	
	var pattern_counts = {}
	
	for sequence in sequences:
		var pattern_key = str(sequence)
		if not pattern_counts.has(pattern_key):
			pattern_counts[pattern_key] = {"pattern": sequence, "frequency": 0}
		pattern_counts[pattern_key].frequency += 1
	
	var patterns = []
	for key in pattern_counts:
		patterns.append(pattern_counts[key])
	
	return patterns

func _pattern_exists(pattern_list: Array, new_pattern: Dictionary) -> bool:
	## Check if a similar pattern already exists

	for existing in pattern_list:
		if existing.type == new_pattern.type:
			# For movement sequences, check if sequence is similar
			if new_pattern.type == "movement_sequence":
				if existing.sequence == new_pattern.sequence:
					return true
			# For other patterns, check type match
			elif existing.type == new_pattern.type:
				return true

	return false

func _predict_next_movement() -> String:
	## Predict the next likely movement based on patterns

	if movement_patterns.is_empty():
		return ""

	# Find the most confident movement pattern
	var best_pattern = null
	var best_confidence = 0.0

	for pattern in movement_patterns:
		if pattern.confidence > best_confidence:
			best_confidence = pattern.confidence
			best_pattern = pattern

	if best_pattern and best_pattern.has("sequence"):
		var sequence = best_pattern.sequence
		if sequence.size() > 0:
			return sequence[0]  # Return first move in sequence

	return ""

func _predict_next_actions() -> Array:
	## Predict the next likely actions based on patterns

	var predicted_actions = []

	if action_patterns.is_empty():
		return predicted_actions

	# Find the most confident action pattern
	var best_pattern = null
	var best_confidence = 0.0

	for pattern in action_patterns:
		if pattern.confidence > best_confidence:
			best_confidence = pattern.confidence
			best_pattern = pattern

	if best_pattern and best_pattern.has("pattern"):
		var pattern_sequence = best_pattern.pattern
		if pattern_sequence.size() > 0:
			predicted_actions = pattern_sequence[0]  # Return first actions in sequence

	return predicted_actions

func _predict_timing() -> Dictionary:
	## Predict timing characteristics

	var timing_prediction = {}

	for pattern in timing_patterns:
		if pattern.type == "response_timing":
			timing_prediction = {
				"expected_response_time": pattern.average_response,
				"consistency": pattern.consistency,
				"variance": pattern.variance
			}
			break

	return timing_prediction

func _get_captain_insights() -> Dictionary:
	## Get insights relevant to captain station

	var insights = {}

	# Movement prediction insights
	var movement_prediction = _predict_next_movement()
	if movement_prediction != "":
		insights["predicted_movement"] = movement_prediction

	# Strategic insights
	for pattern in strategic_patterns:
		if pattern.type == "strategic_preference":
			insights["enemy_aggression"] = pattern.aggression_ratio
			insights["enemy_surface_tendency"] = pattern.surface_ratio

	return insights

func _get_engineer_insights() -> Dictionary:
	## Get insights relevant to engineer station

	var insights = {}

	# System usage patterns
	for pattern in action_patterns:
		if pattern.type == "action_sequence":
			insights["system_usage_pattern"] = pattern.pattern

	return insights

func _get_firstmate_insights() -> Dictionary:
	## Get insights relevant to firstmate station

	var insights = {}

	# Weapon usage patterns
	for pattern in strategic_patterns:
		if pattern.type == "strategic_preference":
			insights["weapon_usage_frequency"] = pattern.weapon_usage_ratio

	return insights

func _get_radio_insights() -> Dictionary:
	## Get insights relevant to radio operator station

	var insights = {}

	# Movement tracking insights
	for pattern in movement_patterns:
		insights["movement_predictability"] = pattern.confidence

	return insights

func _update_pattern_confidence() -> void:
	## Update confidence levels for all patterns

	# Decay confidence over time for old patterns
	var current_time = Time.get_unix_time_from_system()

	for pattern in movement_patterns:
		var age = current_time - pattern.detected_at
		if age > 300:  # 5 minutes
			pattern.confidence *= 0.9  # Decay confidence

	for pattern in action_patterns:
		var age = current_time - pattern.detected_at
		if age > 300:
			pattern.confidence *= 0.9

	# Remove patterns with very low confidence
	movement_patterns = movement_patterns.filter(func(p): return p.confidence > 0.1)
	action_patterns = action_patterns.filter(func(p): return p.confidence > 0.1)

func _strengthen_successful_patterns(predicted: Dictionary, actual: Dictionary) -> void:
	## Strengthen patterns that led to successful predictions

	# Strengthen movement patterns if movement was predicted correctly
	if predicted.has("predicted_movement") and actual.has("movement_direction"):
		if predicted.predicted_movement == actual.movement_direction:
			for pattern in movement_patterns:
				if pattern.has("sequence") and pattern.sequence.size() > 0:
					if pattern.sequence[0] == actual.movement_direction:
						pattern.confidence = min(1.0, pattern.confidence * 1.1)

func _weaken_failed_patterns(predicted: Dictionary, actual: Dictionary) -> void:
	## Weaken patterns that led to failed predictions

	# Weaken movement patterns if movement was predicted incorrectly
	if predicted.has("predicted_movement") and actual.has("movement_direction"):
		if predicted.predicted_movement != actual.movement_direction:
			for pattern in movement_patterns:
				if pattern.has("sequence") and pattern.sequence.size() > 0:
					if pattern.sequence[0] == predicted.predicted_movement:
						pattern.confidence = max(0.1, pattern.confidence * 0.9)

func _calculate_average(values: Array) -> float:
	## Calculate average of numeric values

	if values.is_empty():
		return 0.0

	var sum = 0.0
	for value in values:
		sum += value

	return sum / float(values.size())

func _calculate_variance(values: Array, average: float) -> float:
	## Calculate variance of numeric values

	if values.is_empty():
		return 0.0

	var sum_squared_diff = 0.0
	for value in values:
		var diff = value - average
		sum_squared_diff += diff * diff

	return sum_squared_diff / float(values.size())

func _get_prediction_accuracy() -> float:
	## Get overall prediction accuracy

	if total_predictions == 0:
		return 0.0

	return float(successful_predictions) / float(total_predictions)
