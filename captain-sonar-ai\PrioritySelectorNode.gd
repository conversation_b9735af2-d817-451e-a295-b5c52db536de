class_name PrioritySelectorNode extends CompositeNode

## A selector that executes children based on priority values
## Higher priority children are executed first

var child_priorities: Array[float] = []

func _init(name: String = "PrioritySelector", child_nodes: Array[BehaviorNode] = []) -> void:
	super(name, child_nodes)
	# Initialize priorities to default values
	child_priorities.resize(children.size())
	for i in range(child_priorities.size()):
		child_priorities[i] = 1.0

func add_child_with_priority(child: BehaviorNode, priority: float) -> void:
	## Add a child with a specific priority
	add_child(child)
	child_priorities.append(priority)

func set_child_priority(child_index: int, priority: float) -> void:
	## Set the priority of a specific child
	if child_index >= 0 and child_index < child_priorities.size():
		child_priorities[child_index] = priority

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute children in priority order until one succeeds
	
	# Create sorted indices based on priority (highest first)
	var sorted_indices = []
	for i in range(children.size()):
		sorted_indices.append(i)
	
	sorted_indices.sort_custom(func(a, b): return child_priorities[a] > child_priorities[b])
	
	# Execute children in priority order
	for index in sorted_indices:
		var child = children[index]
		var result = child.execute(ctx)
		
		match result:
			Status.SUCCESS:
				return Status.SUCCESS
			Status.RUNNING:
				return Status.RUNNING
			Status.FAILURE:
				continue
	
	# All children failed
	return Status.FAILURE
