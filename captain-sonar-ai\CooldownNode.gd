class_name CooldownNode extends DecoratorNode

## Prevents its child from executing too frequently
## Useful for actions that should have a minimum time between executions

var cooldown_time: float = 1.0
var last_execution_time: float = 0.0

func _init(name: String = "Cooldown", child_node: BehaviorNode = null, cooldown: float = 1.0) -> void:
	super(name, child_node)
	cooldown_time = cooldown

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child only if cooldown has elapsed
	
	if not child:
		return Status.FAILURE
	
	var current_time = Time.get_unix_time_from_system()
	
	# Check if cooldown has elapsed
	if current_time - last_execution_time < cooldown_time:
		return Status.FAILURE
	
	# Execute child and update last execution time
	var result = child.execute(ctx)
	
	if result != Status.RUNNING:
		last_execution_time = current_time
	
	return result

func reset() -> void:
	## Reset the cooldown timer
	super.reset()
	last_execution_time = 0.0
