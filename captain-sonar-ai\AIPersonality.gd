extends Resource
class_name AIPersonality

## AI Personality Resource - Parameter-Based Difficulty System
##
## This resource replaces the old separate difficulty implementations with
## a single parameter-based system. All AI behavior is controlled through
## these parameters, allowing for fine-tuned difficulty scaling and
## dynamic adjustment during gameplay.
##
## Based on the design document's DifficultyProfile specification.

## Core AI Parameters - these control all AI behavior

@export_group("Decision Making")
## Decision delay multiplier - higher values make AI slower to react
@export var reaction_time: float = 1.0

## Decision accuracy (0-1) - affects how often AI makes optimal choices
@export var accuracy: float = 0.5

## How many events/turns the AI remembers for decision making
@export var memory_span: int = 5

## How many turns ahead the AI plans (strategic thinking depth)
@export var prediction_depth: int = 1

@export_group("Cooperation & Learning")
## Inter-station coordination level (0-1) - how well stations work together
@export var cooperation_level: float = 0.5

## Learning from player patterns (0-1) - how quickly AI adapts to player behavior
@export var adaptability: float = 0.3

@export_group("Combat Behavior")
## Combat preference (0-1) - 0 = defensive, 1 = aggressive
@export var aggression: float = 0.5

## Risk aversion (0-1) - 0 = reckless, 1 = very cautious
@export var caution: float = 0.5

@export_group("Station-Specific Parameters")
## Captain-specific parameters
@export var captain_tactical_awareness: float = 0.5
@export var captain_pathfinding_intelligence: float = 0.5

## Engineer-specific parameters
@export var engineer_repair_efficiency: float = 0.5
@export var engineer_damage_prediction: float = 0.3

## First Mate-specific parameters
@export var first_mate_equipment_prioritization: float = 0.5
@export var first_mate_charging_efficiency: float = 0.5

## Radio Operator-specific parameters
@export var radio_tracking_accuracy: float = 0.5
@export var radio_pattern_recognition: float = 0.3

@export_group("Advanced Features (Hard Mode)")
## Strategic planning capabilities
@export var strategic_planning_enabled: bool = false
@export var multi_turn_planning_depth: int = 3

## Pattern recognition and learning
@export var pattern_recognition_enabled: bool = false
@export var player_modeling_enabled: bool = false

## Advanced coordination
@export var advanced_coordination_enabled: bool = false
@export var psychological_warfare_enabled: bool = false

## Preset difficulty configurations
enum DifficultyPreset {
	EASY,
	NORMAL, 
	HARD,
	CUSTOM
}

var current_preset: DifficultyPreset = DifficultyPreset.NORMAL

func _init() -> void:
	# Set default values for normal difficulty
	apply_preset(DifficultyPreset.NORMAL)

## Public API

func apply_preset(preset: DifficultyPreset) -> void:
	## Apply a difficulty preset configuration
	
	current_preset = preset
	
	match preset:
		DifficultyPreset.EASY:
			_apply_easy_preset()
		DifficultyPreset.NORMAL:
			_apply_normal_preset()
		DifficultyPreset.HARD:
			_apply_hard_preset()
		DifficultyPreset.CUSTOM:
			# Keep current values for custom preset
			pass

func get_reaction_delay() -> float:
	## Get the actual reaction delay in seconds
	return reaction_time * 2.0  # Base delay of 2 seconds

func get_decision_accuracy() -> float:
	## Get decision accuracy as a probability (0-1)
	return clamp(accuracy, 0.0, 1.0)

func should_make_optimal_decision() -> bool:
	## Determine if AI should make the optimal decision based on accuracy
	return randf() < get_decision_accuracy()

func get_memory_capacity() -> int:
	## Get the number of events/turns the AI should remember
	return max(1, memory_span)

func get_planning_depth() -> int:
	## Get how many turns ahead the AI should plan
	return max(1, prediction_depth)

func get_cooperation_strength() -> float:
	## Get inter-station cooperation strength (0-1)
	return clamp(cooperation_level, 0.0, 1.0)

func get_learning_rate() -> float:
	## Get how quickly the AI adapts to player patterns (0-1)
	return clamp(adaptability, 0.0, 1.0)

func get_aggression_level() -> float:
	## Get combat aggression level (0-1)
	return clamp(aggression, 0.0, 1.0)

func get_caution_level() -> float:
	## Get risk aversion level (0-1)
	return clamp(caution, 0.0, 1.0)

func is_strategic_planning_enabled() -> bool:
	## Check if strategic planning features are enabled
	return strategic_planning_enabled and prediction_depth > 1

func is_pattern_recognition_enabled() -> bool:
	## Check if pattern recognition features are enabled
	return pattern_recognition_enabled and adaptability > 0.3

func is_advanced_coordination_enabled() -> bool:
	## Check if advanced coordination features are enabled
	return advanced_coordination_enabled and cooperation_level > 0.7

func get_station_parameters(station_type: String) -> Dictionary:
	## Get parameters specific to a station type
	
	match station_type.to_lower():
		"captain":
			return {
				"tactical_awareness": captain_tactical_awareness,
				"pathfinding_intelligence": captain_pathfinding_intelligence,
				"aggression": get_aggression_level(),
				"caution": get_caution_level()
			}
		"engineer":
			return {
				"repair_efficiency": engineer_repair_efficiency,
				"damage_prediction": engineer_damage_prediction,
				"caution": get_caution_level()
			}
		"first_mate":
			return {
				"equipment_prioritization": first_mate_equipment_prioritization,
				"charging_efficiency": first_mate_charging_efficiency,
				"cooperation": get_cooperation_strength()
			}
		"radio_operator":
			return {
				"tracking_accuracy": radio_tracking_accuracy,
				"pattern_recognition": radio_pattern_recognition,
				"adaptability": get_learning_rate()
			}
		_:
			return {}

func create_copy() -> AIPersonality:
	## Create a copy of this personality for modification
	
	var copy = AIPersonality.new()
	
	# Copy all parameters
	copy.reaction_time = reaction_time
	copy.accuracy = accuracy
	copy.memory_span = memory_span
	copy.prediction_depth = prediction_depth
	copy.cooperation_level = cooperation_level
	copy.adaptability = adaptability
	copy.aggression = aggression
	copy.caution = caution
	
	# Copy station-specific parameters
	copy.captain_tactical_awareness = captain_tactical_awareness
	copy.captain_pathfinding_intelligence = captain_pathfinding_intelligence
	copy.engineer_repair_efficiency = engineer_repair_efficiency
	copy.engineer_damage_prediction = engineer_damage_prediction
	copy.first_mate_equipment_prioritization = first_mate_equipment_prioritization
	copy.first_mate_charging_efficiency = first_mate_charging_efficiency
	copy.radio_tracking_accuracy = radio_tracking_accuracy
	copy.radio_pattern_recognition = radio_pattern_recognition
	
	# Copy advanced features
	copy.strategic_planning_enabled = strategic_planning_enabled
	copy.multi_turn_planning_depth = multi_turn_planning_depth
	copy.pattern_recognition_enabled = pattern_recognition_enabled
	copy.player_modeling_enabled = player_modeling_enabled
	copy.advanced_coordination_enabled = advanced_coordination_enabled
	copy.psychological_warfare_enabled = psychological_warfare_enabled
	
	copy.current_preset = current_preset
	
	return copy

## Private preset methods

func _apply_easy_preset() -> void:
	## Apply easy difficulty parameters
	
	# Basic parameters - slower, less accurate, less strategic
	reaction_time = 2.0
	accuracy = 0.3
	memory_span = 3
	prediction_depth = 1
	cooperation_level = 0.2
	adaptability = 0.1
	aggression = 0.3
	caution = 0.7
	
	# Station-specific parameters - reduced capabilities
	captain_tactical_awareness = 0.2
	captain_pathfinding_intelligence = 0.3
	engineer_repair_efficiency = 0.3
	engineer_damage_prediction = 0.1
	first_mate_equipment_prioritization = 0.3
	first_mate_charging_efficiency = 0.4
	radio_tracking_accuracy = 0.3
	radio_pattern_recognition = 0.1
	
	# Advanced features disabled
	strategic_planning_enabled = false
	pattern_recognition_enabled = false
	player_modeling_enabled = false
	advanced_coordination_enabled = false
	psychological_warfare_enabled = false

func _apply_normal_preset() -> void:
	## Apply normal difficulty parameters
	
	# Basic parameters - balanced capabilities
	reaction_time = 1.0
	accuracy = 0.5
	memory_span = 5
	prediction_depth = 1
	cooperation_level = 0.5
	adaptability = 0.3
	aggression = 0.5
	caution = 0.5
	
	# Station-specific parameters - moderate capabilities
	captain_tactical_awareness = 0.5
	captain_pathfinding_intelligence = 0.5
	engineer_repair_efficiency = 0.5
	engineer_damage_prediction = 0.3
	first_mate_equipment_prioritization = 0.5
	first_mate_charging_efficiency = 0.6
	radio_tracking_accuracy = 0.5
	radio_pattern_recognition = 0.3
	
	# Some advanced features enabled
	strategic_planning_enabled = false
	pattern_recognition_enabled = true
	player_modeling_enabled = false
	advanced_coordination_enabled = false
	psychological_warfare_enabled = false

func _apply_hard_preset() -> void:
	## Apply hard difficulty parameters
	
	# Basic parameters - high capabilities
	reaction_time = 0.5
	accuracy = 0.8
	memory_span = 10
	prediction_depth = 3
	cooperation_level = 0.8
	adaptability = 0.7
	aggression = 0.6
	caution = 0.4
	
	# Station-specific parameters - high capabilities
	captain_tactical_awareness = 0.8
	captain_pathfinding_intelligence = 0.8
	engineer_repair_efficiency = 0.8
	engineer_damage_prediction = 0.7
	first_mate_equipment_prioritization = 0.8
	first_mate_charging_efficiency = 0.8
	radio_tracking_accuracy = 0.8
	radio_pattern_recognition = 0.7
	
	# All advanced features enabled
	strategic_planning_enabled = true
	multi_turn_planning_depth = 5
	pattern_recognition_enabled = true
	player_modeling_enabled = true
	advanced_coordination_enabled = true
	psychological_warfare_enabled = true

## Debug and utility methods

func get_preset_name() -> String:
	## Get the name of the current preset
	
	match current_preset:
		DifficultyPreset.EASY:
			return "Easy"
		DifficultyPreset.NORMAL:
			return "Normal"
		DifficultyPreset.HARD:
			return "Hard"
		DifficultyPreset.CUSTOM:
			return "Custom"
		_:
			return "Unknown"

func print_parameters() -> void:
	## Print all parameters for debugging
	
	print("=== AI Personality Parameters ===")
	print("Preset: ", get_preset_name())
	print("Reaction Time: ", reaction_time)
	print("Accuracy: ", accuracy)
	print("Memory Span: ", memory_span)
	print("Prediction Depth: ", prediction_depth)
	print("Cooperation Level: ", cooperation_level)
	print("Adaptability: ", adaptability)
	print("Aggression: ", aggression)
	print("Caution: ", caution)
	print("Strategic Planning: ", strategic_planning_enabled)
	print("Pattern Recognition: ", pattern_recognition_enabled)
	print("Advanced Coordination: ", advanced_coordination_enabled)
