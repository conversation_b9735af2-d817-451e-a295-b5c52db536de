extends Node
class_name AIHardModeManager

## Hard Mode AI Manager - Advanced AI Capabilities
##
## This system manages all advanced AI features for hard difficulty including:
## - Strategic planning with multi-turn lookahead
## - Pattern recognition and player modeling
## - Advanced coordination between stations
## - Machine learning and adaptation
## - Psychological warfare tactics

signal hard_mode_activated()
signal strategic_plan_updated(plan: Dictionary)
signal pattern_detected(pattern: Dictionary)
signal player_model_updated(model: Dictionary)

# Core systems
var strategic_planner: AIStrategicPlanner
var pattern_analyzer: AIPatternAnalyzer
var player_modeler: AIPlayerModeler
var coordination_system: AIAdvancedCoordination

# Hard mode state
var is_hard_mode_active: bool = false
var current_strategic_plan: Dictionary = {}
var player_behavior_model: Dictionary = {}
var detected_patterns: Array[Dictionary] = []

# Performance tracking
var strategic_decisions_made: int = 0
var patterns_recognized: int = 0
var successful_predictions: int = 0
var total_predictions: int = 0

# Configuration
var enable_strategic_planning: bool = true
var enable_pattern_recognition: bool = true
var enable_player_modeling: bool = true
var enable_advanced_coordination: bool = true
var enable_psychological_warfare: bool = true

func _init():
	name = "AIHardModeManager"

func initialize_hard_mode(personality: AIPersonality) -> void:
	## Initialize all hard mode systems
	
	if not personality.strategic_planning_enabled:
		print("AIHardModeManager: Hard mode features disabled by personality")
		return
	
	print("AIHardModeManager: Initializing hard mode systems...")
	
	# Initialize strategic planner
	if enable_strategic_planning:
		strategic_planner = AIStrategicPlanner.new()
		strategic_planner.strategic_plan_updated.connect(_on_strategic_plan_updated)
		add_child(strategic_planner)
		print("AIHardModeManager: Strategic planner initialized")
	
	# Initialize pattern analyzer
	if enable_pattern_recognition:
		pattern_analyzer = AIPatternAnalyzer.new()
		pattern_analyzer.pattern_detected.connect(_on_pattern_detected)
		add_child(pattern_analyzer)
		print("AIHardModeManager: Pattern analyzer initialized")
	
	# Initialize player modeler
	if enable_player_modeling:
		player_modeler = AIPlayerModeler.new()
		player_modeler.model_updated.connect(_on_player_model_updated)
		add_child(player_modeler)
		print("AIHardModeManager: Player modeler initialized")
	
	# Initialize advanced coordination
	if enable_advanced_coordination:
		coordination_system = AIAdvancedCoordination.new()
		add_child(coordination_system)
		print("AIHardModeManager: Advanced coordination initialized")
	
	is_hard_mode_active = true
	hard_mode_activated.emit()
	print("AIHardModeManager: Hard mode fully activated")

func process_turn(game_state: Dictionary, personality: AIPersonality) -> Dictionary:
	## Process a turn with hard mode AI capabilities
	
	if not is_hard_mode_active:
		return {}
	
	var hard_mode_decisions = {}
	
	# Update player model with current observations
	if player_modeler:
		player_modeler.update_model(game_state)
	
	# Analyze patterns in player behavior
	if pattern_analyzer:
		pattern_analyzer.analyze_turn(game_state)
	
	# Create or update strategic plan
	if strategic_planner:
		var strategic_input = _prepare_strategic_input(game_state)
		current_strategic_plan = strategic_planner.create_strategic_plan(strategic_input, personality)
		hard_mode_decisions["strategic_plan"] = current_strategic_plan
		strategic_decisions_made += 1
	
	# Generate advanced coordination directives
	if coordination_system:
		var coordination_directives = coordination_system.generate_directives(game_state, current_strategic_plan)
		hard_mode_decisions["coordination"] = coordination_directives
	
	# Apply psychological warfare if enabled
	if enable_psychological_warfare:
		var psych_tactics = _generate_psychological_tactics(game_state)
		hard_mode_decisions["psychological_tactics"] = psych_tactics
	
	return hard_mode_decisions

func get_strategic_recommendation(station_name: String, context: Dictionary) -> Dictionary:
	## Get strategic recommendation for a specific station
	
	if not is_hard_mode_active or not strategic_planner:
		return {}
	
	var recommendation = {}
	
	# Get station-specific strategic guidance
	if current_strategic_plan.has("station_directives"):
		var station_directives = current_strategic_plan["station_directives"]
		if station_directives.has(station_name):
			recommendation = station_directives[station_name]
	
	# Add pattern-based insights
	if pattern_analyzer:
		var pattern_insights = pattern_analyzer.get_insights_for_station(station_name)
		recommendation["pattern_insights"] = pattern_insights
	
	# Add player model predictions
	if player_modeler:
		var predictions = player_modeler.predict_player_actions(context)
		recommendation["player_predictions"] = predictions
	
	return recommendation

func predict_enemy_behavior(current_state: Dictionary, turns_ahead: int = 3) -> Array[Dictionary]:
	## Predict enemy behavior for multiple turns ahead
	
	var predictions: Array[Dictionary] = []
	
	if not is_hard_mode_active:
		return predictions
	
	# Use player model for predictions
	if player_modeler:
		predictions = player_modeler.predict_sequence(current_state, turns_ahead)
		total_predictions += predictions.size()
	
	# Enhance with pattern analysis
	if pattern_analyzer:
		for i in range(predictions.size()):
			var pattern_enhancement = pattern_analyzer.enhance_prediction(predictions[i])
			predictions[i].merge(pattern_enhancement)
	
	return predictions

func validate_prediction(predicted: Dictionary, actual: Dictionary) -> void:
	## Validate a prediction against actual outcome for learning
	
	if not is_hard_mode_active:
		return
	
	var accuracy = _calculate_prediction_accuracy(predicted, actual)
	
	if accuracy > 0.7:  # Consider prediction successful if >70% accurate
		successful_predictions += 1
	
	# Update learning systems
	if player_modeler:
		player_modeler.update_from_validation(predicted, actual, accuracy)
	
	if pattern_analyzer:
		pattern_analyzer.update_from_validation(predicted, actual, accuracy)

func get_hard_mode_status() -> Dictionary:
	## Get comprehensive status of hard mode systems
	
	var status = {
		"active": is_hard_mode_active,
		"strategic_decisions_made": strategic_decisions_made,
		"patterns_recognized": patterns_recognized,
		"prediction_accuracy": _get_prediction_accuracy(),
		"systems_status": {}
	}
	
	if strategic_planner:
		status["systems_status"]["strategic_planner"] = strategic_planner.get_status()
	
	if pattern_analyzer:
		status["systems_status"]["pattern_analyzer"] = pattern_analyzer.get_status()
	
	if player_modeler:
		status["systems_status"]["player_modeler"] = player_modeler.get_status()
	
	if coordination_system:
		status["systems_status"]["coordination"] = coordination_system.get_status()
	
	return status

func _prepare_strategic_input(game_state: Dictionary) -> Dictionary:
	## Prepare input for strategic planning system
	
	var strategic_input = game_state.duplicate(true)
	
	# Add player model insights
	if player_modeler:
		strategic_input["player_model"] = player_modeler.get_current_model()
	
	# Add detected patterns
	strategic_input["detected_patterns"] = detected_patterns
	
	# Add prediction history
	strategic_input["prediction_accuracy"] = _get_prediction_accuracy()
	
	return strategic_input

func _generate_psychological_tactics(game_state: Dictionary) -> Dictionary:
	## Generate psychological warfare tactics
	
	var tactics = {}
	
	if not enable_psychological_warfare:
		return tactics
	
	# Analyze player stress indicators
	var stress_level = _analyze_player_stress(game_state)
	
	if stress_level > 0.6:
		tactics["increase_pressure"] = true
		tactics["rapid_actions"] = true
	elif stress_level < 0.3:
		tactics["unpredictable_behavior"] = true
		tactics["misdirection"] = true
	
	# Use pattern insights for psychological pressure
	if pattern_analyzer:
		var player_weaknesses = pattern_analyzer.identify_weaknesses()
		tactics["exploit_weaknesses"] = player_weaknesses
	
	return tactics

func _analyze_player_stress(game_state: Dictionary) -> float:
	## Analyze indicators of player stress level
	
	var stress_indicators = 0.0
	var total_indicators = 0.0
	
	# Health-based stress
	var our_health = game_state.get("our_health", 4)
	var enemy_health = game_state.get("enemy_health", 4)
	
	if enemy_health < our_health:
		stress_indicators += 0.3
	total_indicators += 0.3
	
	# Time pressure
	var turn_count = game_state.get("turn_count", 0)
	if turn_count > 20:
		stress_indicators += 0.2
	total_indicators += 0.2
	
	# Detection pressure
	if game_state.get("enemy_detected", false):
		stress_indicators += 0.5
	total_indicators += 0.5
	
	return stress_indicators / max(total_indicators, 1.0)

func _calculate_prediction_accuracy(predicted: Dictionary, actual: Dictionary) -> float:
	## Calculate accuracy of a prediction
	
	var correct_predictions = 0.0
	var total_predictions_made = 0.0
	
	for key in predicted:
		if actual.has(key):
			total_predictions_made += 1.0
			if predicted[key] == actual[key]:
				correct_predictions += 1.0
			elif typeof(predicted[key]) == TYPE_FLOAT and typeof(actual[key]) == TYPE_FLOAT:
				# For float values, consider close values as correct
				var difference = abs(predicted[key] - actual[key])
				if difference < 0.1:
					correct_predictions += 1.0
	
	return correct_predictions / max(total_predictions_made, 1.0)

func _get_prediction_accuracy() -> float:
	## Get overall prediction accuracy
	
	if total_predictions == 0:
		return 0.0
	
	return float(successful_predictions) / float(total_predictions)

# Signal handlers
func _on_strategic_plan_updated(plan: Dictionary) -> void:
	current_strategic_plan = plan
	strategic_plan_updated.emit(plan)

func _on_pattern_detected(pattern: Dictionary) -> void:
	detected_patterns.append(pattern)
	patterns_recognized += 1
	pattern_detected.emit(pattern)
	
	# Keep only recent patterns
	if detected_patterns.size() > 20:
		detected_patterns.pop_front()

func _on_player_model_updated(model: Dictionary) -> void:
	player_behavior_model = model
	player_model_updated.emit(model)
