extends AIStation
class_name <PERSON><PERSON><PERSON>tain

## AI Captain Station - Strategic Command and Movement
##
## The Captain AI is responsible for:
## - High-level strategic decisions and movement planning
## - Combat coordination and tactical positioning
## - Emergency response and damage control coordination
## - Inter-station communication and command decisions
##
## Uses behavior tree-based decision making with parameter-based difficulty scaling.

signal movement_decision(direction: String)

## Captain-specific state
var current_strategy: String = "patrol"
var last_enemy_contact_turn: int = 0
var movement_pattern: Array[String] = []
var combat_readiness: float = 0.5

## Strategic parameters (from personality)
var aggression_level: float = 0.5
var caution_level: float = 0.5
var tactical_awareness: float = 0.5

func _init() -> void:
	super()
	station_name = "captain"
	station_type = "captain"

func create_behavior_tree() -> BehaviorNode:
	## Create the Captain's behavior tree
	## This implements the strategic decision-making process

	# Root selector - choose between different strategic modes
	var root = SelectorNode.new("CaptainRoot")

	# Emergency response (highest priority)
	var emergency_sequence = SequenceNode.new("EmergencyResponse")
	emergency_sequence.add_child(HealthCriticalCondition.new())
	emergency_sequence.add_child(_create_emergency_behavior())

	# Combat mode (when enemy is detected and close)
	var combat_sequence = SequenceNode.new("CombatMode")
	combat_sequence.add_child(EnemyDetectedCondition.new(0.7))  # High certainty
	combat_sequence.add_child(EnemyCloseCondition.new(4.0))
	combat_sequence.add_child(_create_combat_behavior())

	# Hunt mode (when enemy is detected but not close)
	var hunt_sequence = SequenceNode.new("HuntMode")
	hunt_sequence.add_child(EnemyDetectedCondition.new(0.4))  # Medium certainty
	hunt_sequence.add_child(InverterNode.new("NotClose", EnemyCloseCondition.new(4.0)))
	hunt_sequence.add_child(_create_hunt_behavior())

	# Patrol mode (default behavior)
	var patrol_behavior = _create_patrol_behavior()

	# Add all behaviors to root selector
	root.add_child(emergency_sequence)
	root.add_child(combat_sequence)
	root.add_child(hunt_sequence)
	root.add_child(patrol_behavior)

	return root

func _create_emergency_behavior() -> BehaviorNode:
	## Create emergency response behavior tree

	var emergency_selector = SelectorNode.new("EmergencySelector")

	# Try to surface if possible
	var surface_sequence = SequenceNode.new("SurfaceSequence")
	surface_sequence.add_child(InverterNode.new("NotSurfaced", SurfacedCondition.new()))
	surface_sequence.add_child(SurfaceAction.new())

	# Otherwise, evade
	var evade_action = EvadeAction.new()

	emergency_selector.add_child(surface_sequence)
	emergency_selector.add_child(evade_action)

	return emergency_selector

func _create_combat_behavior() -> BehaviorNode:
	## Create combat behavior tree

	var combat_selector = PrioritySelectorNode.new("CombatSelector")

	# High priority: Attack if weapons ready
	var attack_sequence = SequenceNode.new("AttackSequence")
	attack_sequence.add_child(EquipmentReadyCondition.new("torpedo"))
	attack_sequence.add_child(AttackAction.new("torpedo"))

	# Medium priority: Charge weapons if not ready
	var charge_sequence = SequenceNode.new("ChargeSequence")
	charge_sequence.add_child(InverterNode.new("WeaponsNotReady", EquipmentReadyCondition.new("torpedo")))
	charge_sequence.add_child(ChargeEquipmentAction.new("torpedo"))

	# Low priority: Tactical movement
	var tactical_move = _create_tactical_movement()

	# Add with priorities (higher number = higher priority)
	combat_selector.add_child_with_priority(attack_sequence, 10)
	combat_selector.add_child_with_priority(charge_sequence, 5)
	combat_selector.add_child_with_priority(tactical_move, 1)

	return combat_selector

func _create_hunt_behavior() -> BehaviorNode:
	## Create hunting behavior tree

	var hunt_sequence = SequenceNode.new("HuntSequence")

	# Track enemy movement
	hunt_sequence.add_child(TrackEnemyAction.new())

	# Move toward enemy with some randomness
	var hunt_selector = WeightedSelectorNode.new("HuntMovement")

	# 70% chance to move toward enemy
	var pursue_action = _create_pursuit_movement()
	hunt_selector.add_child_with_weight(pursue_action, 0.7)

	# 30% chance for tactical positioning
	var tactical_action = _create_tactical_movement()
	hunt_selector.add_child_with_weight(tactical_action, 0.3)

	hunt_sequence.add_child(hunt_selector)

	return hunt_sequence

func _create_patrol_behavior() -> BehaviorNode:
	## Create patrol behavior tree

	var patrol_selector = WeightedSelectorNode.new("PatrolSelector")

	# Weight patrol actions based on personality
	var patrol_weight = 0.6 + (caution_level * 0.3)
	var random_weight = 0.4 - (caution_level * 0.3)

	# Structured patrol
	var patrol_action = PatrolAction.new(_generate_patrol_points())
	patrol_selector.add_child_with_weight(patrol_action, patrol_weight)

	# Random exploration
	var random_action = RandomMoveAction.new()
	patrol_selector.add_child_with_weight(random_action, random_weight)

	return patrol_selector

func _create_tactical_movement() -> BehaviorNode:
	## Create tactical movement behavior

	var tactical_selector = SelectorNode.new("TacticalMovement")

	# If enemy is very close, evade
	var evade_sequence = SequenceNode.new("EvadeSequence")
	evade_sequence.add_child(EnemyCloseCondition.new(2.0))
	evade_sequence.add_child(EvadeAction.new())

	# Otherwise, position strategically
	var position_action = _create_strategic_positioning()

	tactical_selector.add_child(evade_sequence)
	tactical_selector.add_child(position_action)

	return tactical_selector

func _create_pursuit_movement() -> BehaviorNode:
	## Create pursuit movement behavior

	return PursuitAction.new()

func _create_strategic_positioning() -> BehaviorNode:
	## Create strategic positioning behavior

	var positioning_selector = WeightedSelectorNode.new("StrategicPositioning")

	# Weights based on personality
	var aggressive_weight = aggression_level
	var defensive_weight = caution_level
	var neutral_weight = 1.0 - (aggression_level + caution_level) * 0.5

	# Aggressive positioning (move toward enemy)
	var aggressive_action = PursuitAction.new()
	positioning_selector.add_child_with_weight(aggressive_action, aggressive_weight)

	# Defensive positioning (maintain distance)
	var defensive_action = DefensivePositionAction.new()
	positioning_selector.add_child_with_weight(defensive_action, defensive_weight)

	# Neutral movement (patrol/random)
	var neutral_action = RandomMoveAction.new()
	positioning_selector.add_child_with_weight(neutral_action, neutral_weight)

	return positioning_selector

func _generate_patrol_points() -> Array[Vector2i]:
	## Generate patrol points based on current strategy

	# Default patrol pattern - can be made more sophisticated
	return [
		Vector2i(3, 3),
		Vector2i(8, 3),
		Vector2i(8, 8),
		Vector2i(3, 8),
		Vector2i(5, 5)
	]

## Station-specific overrides

func _initialize_station() -> void:
	## Initialize Captain-specific systems

	# Set initial strategy
	current_strategy = "patrol"

	# Initialize movement pattern
	movement_pattern = ["NORTH", "EAST", "SOUTH", "WEST"]

	if debug_mode:
		print("AICaptain: Captain station initialized")

func _on_personality_updated(new_personality: AIPersonality) -> void:
	## Handle personality parameter updates

	var captain_params = new_personality.get_station_parameters("captain")

	aggression_level = captain_params.get("aggression", 0.5)
	caution_level = captain_params.get("caution", 0.5)
	tactical_awareness = captain_params.get("tactical_awareness", 0.5)

	if debug_mode:
		print("AICaptain: Updated personality - Aggression: ", aggression_level,
			  ", Caution: ", caution_level, ", Tactical: ", tactical_awareness)

func _subscribe_to_events() -> void:
	## Subscribe to Captain-relevant events

	super._subscribe_to_events()

	if ai_event_bus:
		ai_event_bus.subscribe(self, "enemy_detected", _on_enemy_detected)
		ai_event_bus.subscribe(self, "enemy_lost", _on_enemy_lost)
		ai_event_bus.subscribe(self, "damage_taken", _on_damage_taken)
		ai_event_bus.subscribe(self, "combat_opportunity", _on_combat_opportunity)

func _on_enemy_detected(event: Dictionary) -> void:
	## Handle enemy detection

	var enemy_data = event.data
	last_enemy_contact_turn = enemy_data.get("turn", 0)

	# Update strategy based on detection
	if enemy_data.get("certainty", 0.0) > 0.7:
		current_strategy = "combat"
	else:
		current_strategy = "hunt"

	if debug_mode:
		print("AICaptain: Enemy detected, switching to ", current_strategy, " strategy")

func _on_enemy_lost(event: Dictionary) -> void:
	## Handle loss of enemy contact

	current_strategy = "patrol"

	if debug_mode:
		print("AICaptain: Enemy contact lost, returning to patrol")

func _on_damage_taken(event: Dictionary) -> void:
	## Handle damage events

	var damage_data = event.data
	var health = damage_data.get("health", 4)

	if health <= 1:
		current_strategy = "emergency"

		if debug_mode:
			print("AICaptain: Critical damage, switching to emergency mode")

func _on_combat_opportunity(event: Dictionary) -> void:
	## Handle combat opportunities

	var opportunity_data = event.data
	combat_readiness = opportunity_data.get("readiness", 0.5)

	if combat_readiness > 0.8:
		current_strategy = "combat"

		if debug_mode:
			print("AICaptain: High combat readiness, engaging")

func get_captain_status() -> Dictionary:
	## Get Captain-specific status information

	var base_status = get_status()
	base_status.merge({
		"current_strategy": current_strategy,
		"last_enemy_contact_turn": last_enemy_contact_turn,
		"combat_readiness": combat_readiness,
		"aggression_level": aggression_level,
		"caution_level": caution_level,
		"tactical_awareness": tactical_awareness
	})

	return base_status

## Legacy compatibility methods for backward compatibility

func setup(personality: AIPersonality, event_bus: AIEventBus, behavior_manager: AIBehaviorManager) -> void:
	## Override parent setup method
	super.setup(personality, event_bus, behavior_manager)

func is_done() -> bool:
	## Legacy method for backward compatibility
	return get_turn_complete_status()

func set_game_over(is_over: bool) -> void:
	## Legacy method for backward compatibility
	pass

func process_station() -> void:
	## Legacy method for backward compatibility
	start_turn()

func get_wants() -> Array[bool]:
	## Legacy method for backward compatibility
	return [false, false, false]

func set_wants(new_wants: Array[bool]) -> void:
	## Legacy method for backward compatibility
	pass

func get_enemy_position() -> Vector2i:
	## Legacy method for backward compatibility
	if behavior_context and behavior_context.blackboard:
		return behavior_context.blackboard.get("enemy_position", Vector2i.ZERO)
	return Vector2i.ZERO

func get_enemy_certainty() -> float:
	## Legacy method for backward compatibility
	if behavior_context and behavior_context.blackboard:
		return behavior_context.blackboard.get("enemy_certainty", 0.0)
	return 0.0

func update_enemy_info(pos: Vector2i, certainty: float) -> void:
	## Legacy method for backward compatibility
	if behavior_context and behavior_context.blackboard:
		behavior_context.blackboard["enemy_position"] = pos
		behavior_context.blackboard["enemy_certainty"] = certainty

func get_debug_info() -> Dictionary:
	## Legacy method for backward compatibility
	return get_captain_status()
