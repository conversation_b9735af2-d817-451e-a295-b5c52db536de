extends Node
class_name AIEventBus

## AI Event Bus for Inter-Station Communication
##
## This class provides event-driven communication between AI stations,
## replacing the tight coupling of the old system. Stations can emit
## events and subscribe to events they're interested in.
##
## Key Features:
## - Decoupled communication between stations
## - Event filtering and routing
## - Event history for debugging
## - Performance monitoring of event flow

## Event history for debugging and analysis
var event_history: Array[Dictionary] = []
var max_history_size: int = 100

## Event subscribers - stations can subscribe to specific event types
var subscribers: Dictionary = {}

## Debug settings
var debug_mode: bool = false
var log_all_events: bool = false

func _ready() -> void:
	if debug_mode:
		print("AIEventBus: Event bus initialized")

## Public API

func emit_event(event_type: String, data: Dictionary = {}) -> void:
	## Emit an event to all interested subscribers
	
	var event = {
		"type": event_type,
		"data": data,
		"timestamp": Time.get_unix_time_from_system(),
		"frame": Engine.get_process_frames()
	}
	
	# Add to history
	_add_to_history(event)
	
	# Log if debugging
	if debug_mode or log_all_events:
		print("AIEventBus: Event emitted - ", event_type, " with data: ", data)
	
	# Notify all subscribers
	_notify_subscribers(event)

func subscribe(subscriber: Node, event_type: String, callback: Callable) -> void:
	## Subscribe a node to a specific event type
	
	if not subscribers.has(event_type):
		subscribers[event_type] = []
	
	var subscription = {
		"subscriber": subscriber,
		"callback": callback,
		"subscription_time": Time.get_unix_time_from_system()
	}
	
	subscribers[event_type].append(subscription)
	
	if debug_mode:
		print("AIEventBus: ", subscriber.name, " subscribed to ", event_type)

func unsubscribe(subscriber: Node, event_type: String) -> void:
	## Unsubscribe a node from a specific event type
	
	if not subscribers.has(event_type):
		return
	
	var updated_subscribers = []
	for subscription in subscribers[event_type]:
		if subscription.subscriber != subscriber:
			updated_subscribers.append(subscription)
	
	subscribers[event_type] = updated_subscribers
	
	if debug_mode:
		print("AIEventBus: ", subscriber.name, " unsubscribed from ", event_type)

func unsubscribe_all(subscriber: Node) -> void:
	## Unsubscribe a node from all event types
	
	for event_type in subscribers:
		unsubscribe(subscriber, event_type)

func get_event_history(event_type: String = "") -> Array[Dictionary]:
	## Get event history, optionally filtered by event type
	
	if event_type == "":
		return event_history.duplicate()
	
	var filtered_history: Array[Dictionary] = []
	for event in event_history:
		if event.type == event_type:
			filtered_history.append(event)
	
	return filtered_history

func get_subscriber_count(event_type: String) -> int:
	## Get the number of subscribers for a specific event type
	
	if not subscribers.has(event_type):
		return 0
	
	return subscribers[event_type].size()

func clear_history() -> void:
	## Clear the event history
	
	event_history.clear()
	
	if debug_mode:
		print("AIEventBus: Event history cleared")

func get_statistics() -> Dictionary:
	## Get event bus statistics for monitoring and debugging
	
	var stats = {
		"total_events": event_history.size(),
		"event_types": {},
		"subscriber_counts": {},
		"recent_activity": []
	}
	
	# Count events by type
	for event in event_history:
		if not stats.event_types.has(event.type):
			stats.event_types[event.type] = 0
		stats.event_types[event.type] += 1
	
	# Count subscribers by type
	for event_type in subscribers:
		stats.subscriber_counts[event_type] = subscribers[event_type].size()
	
	# Get recent activity (last 10 events)
	var recent_count = min(10, event_history.size())
	if recent_count > 0:
		stats.recent_activity = event_history.slice(-recent_count)
	
	return stats

## Private methods

func _add_to_history(event: Dictionary) -> void:
	## Add event to history with size management
	
	event_history.append(event)
	
	# Maintain history size limit
	if event_history.size() > max_history_size:
		event_history.pop_front()

func _notify_subscribers(event: Dictionary) -> void:
	## Notify all subscribers of an event
	
	if not subscribers.has(event.type):
		return
	
	# Create a copy of subscribers to avoid modification during iteration
	var current_subscribers = subscribers[event.type].duplicate()
	
	for subscription in current_subscribers:
		var subscriber = subscription.subscriber
		var callback = subscription.callback
		
		# Check if subscriber is still valid
		if not is_instance_valid(subscriber):
			_cleanup_invalid_subscriber(subscriber, event.type)
			continue
		
		# Call the callback safely
		try:
			callback.call(event)
		except:
			if debug_mode:
				print("AIEventBus: Error calling callback for ", subscriber.name, " on event ", event.type)

func _cleanup_invalid_subscriber(invalid_subscriber: Node, event_type: String) -> void:
	## Remove invalid subscribers from the subscription list
	
	if not subscribers.has(event_type):
		return
	
	var updated_subscribers = []
	for subscription in subscribers[event_type]:
		if subscription.subscriber != invalid_subscriber:
			updated_subscribers.append(subscription)
	
	subscribers[event_type] = updated_subscribers
	
	if debug_mode:
		print("AIEventBus: Cleaned up invalid subscriber from ", event_type)

## Convenience methods for common events

func emit_turn_started(turn_number: int) -> void:
	## Convenience method for turn start events
	emit_event("turn_started", {"turn": turn_number})

func emit_turn_completed(turn_number: int) -> void:
	## Convenience method for turn completion events
	emit_event("turn_completed", {"turn": turn_number})

func emit_game_state_updated(position: Vector2i, health: int, surfaced: bool) -> void:
	## Convenience method for game state updates
	emit_event("game_state_updated", {
		"position": position,
		"health": health,
		"surfaced": surfaced
	})

func emit_enemy_position_updated(position: Vector2i, certainty: float, source: String) -> void:
	## Convenience method for enemy position updates
	emit_event("enemy_position_updated", {
		"position": position,
		"certainty": certainty,
		"source": source
	})

func emit_system_damaged(system_name: String, severity: String) -> void:
	## Convenience method for system damage events
	emit_event("system_damaged", {
		"system": system_name,
		"severity": severity
	})

func emit_equipment_ready(equipment_name: String, station: String) -> void:
	## Convenience method for equipment ready events
	emit_event("equipment_ready", {
		"equipment": equipment_name,
		"station": station
	})

func emit_intelligence_gathered(intelligence_type: String, data: Dictionary) -> void:
	## Convenience method for intelligence gathering events
	emit_event("intelligence_gathered", {
		"type": intelligence_type,
		"data": data
	})

## Debug and monitoring methods

func enable_debug_mode(enable: bool = true) -> void:
	## Enable or disable debug mode
	debug_mode = enable
	if debug_mode:
		print("AIEventBus: Debug mode enabled")

func enable_event_logging(enable: bool = true) -> void:
	## Enable or disable logging of all events
	log_all_events = enable
	if log_all_events:
		print("AIEventBus: Event logging enabled")

func print_statistics() -> void:
	## Print current event bus statistics
	var stats = get_statistics()
	print("=== AIEventBus Statistics ===")
	print("Total events: ", stats.total_events)
	print("Event types: ", stats.event_types)
	print("Subscriber counts: ", stats.subscriber_counts)
	print("Recent activity count: ", stats.recent_activity.size())

func validate_subscribers() -> void:
	## Validate all subscribers and clean up invalid ones
	var cleaned_count = 0
	
	for event_type in subscribers:
		var valid_subscribers = []
		for subscription in subscribers[event_type]:
			if is_instance_valid(subscription.subscriber):
				valid_subscribers.append(subscription)
			else:
				cleaned_count += 1
		subscribers[event_type] = valid_subscribers
	
	if debug_mode and cleaned_count > 0:
		print("AIEventBus: Cleaned up ", cleaned_count, " invalid subscribers")
