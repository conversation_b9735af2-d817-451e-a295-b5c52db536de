extends LeafNode
class_name ConditionNode

## Base Condition Node
##
## Base class for all condition nodes that check game state in behavior trees.
## Conditions return SUCCESS if the condition is met, FAILURE otherwise.

func _init(name: String = "Condition") -> void:
	super(name)

## Override this method in subclasses to implement the condition check
func _execute_implementation(ctx: BehaviorContext) -> Status:
	push_warning("ConditionNode: _execute_implementation not implemented in " + node_name)
	return Status.FAILURE
