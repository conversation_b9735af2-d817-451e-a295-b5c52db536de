extends ActionNode
class_name UseEquipmentAction

## Use Equipment Action
##
## Action to use specific equipment

var equipment_name: String = ""
var target_position: Vector2i = Vector2i.ZERO

func _init(equipment: String, target: Vector2i = Vector2i.ZERO) -> void:
	super("UseEquipment_" + equipment)
	equipment_name = equipment
	target_position = target

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.can_use_equipment(equipment_name):
		return Status.FAILURE
	
	var event_data = {"equipment": equipment_name}
	if target_position != Vector2i.ZERO:
		event_data["target"] = target_position
	
	if debug_enabled:
		print("UseEquipmentAction: Using ", equipment_name, " at ", target_position)
	
	ctx.emit_event("equipment_use", event_data)
	return Status.SUCCESS
