class_name SurfaceAction extends ActionNode

## Action to surface the submarine

func _init(name: String = "Surface") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute surface action

	# Emit surface command event
	ctx.emit_event("surface_command", {})

	return Status.SUCCESS
## Action to surface the submarine

func _init() -> void:
	super("Surface")

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if debug_enabled:
		print("SurfaceAction: Recommending surface")
	
	ctx.emit_event("surface_recommended", {"should_surface": true})
	return Status.SUCCESS
