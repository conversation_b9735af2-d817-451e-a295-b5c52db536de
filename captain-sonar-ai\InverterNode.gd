class_name Inverter<PERSON>ode extends DecoratorNode

## Inverts the result of its child node
## SUCCESS becomes FAILURE, FAILURE becomes SUCCESS, RUNNING stays RUNNING

func _init(name: String = "Inverter", child_node: BehaviorNode = null) -> void:
	super(name, child_node)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute child and invert the result
	
	if not child:
		return Status.FAILURE
	
	var result = child.execute(ctx)
	
	match result:
		Status.SUCCESS:
			return Status.FAILURE
		Status.FAILURE:
			return Status.SUCCESS
		Status.RUNNING:
			return Status.RUNNING
		_:
			return Status.FAILURE
