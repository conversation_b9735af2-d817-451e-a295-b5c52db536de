extends ActionNode
class_name AttackAction

## Attack Action
##
## Action to attack the enemy

var weapon_type: String = "torpedo"

func _init(weapon: String = "torpedo") -> void:
	super("Attack_" + weapon)
	weapon_type = weapon

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.is_enemy_detected():
		return Status.FAILURE
	
	if not ctx.can_use_equipment(weapon_type):
		return Status.FAILURE
	
	if debug_enabled:
		print("AttackAction: Attacking with ", weapon_type, " at ", ctx.enemy_position)
	
	ctx.emit_event("combat_action", {
		"action": "attack",
		"weapon": weapon_type,
		"target": ctx.enemy_position
	})
	
	return Status.SUCCESS
