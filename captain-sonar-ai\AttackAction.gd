class_name AttackAction extends ActionNode

## Action to attack enemy submarine

func _init(name: String = "Attack") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute attack on enemy

	# Prepare attack data
	var attack_data = {
		"target_position": ctx.enemy_position,
		"certainty": ctx.enemy_certainty,
		"attack_type": "torpedo"
	}

	# Emit attack command event
	ctx.emit_event("attack_command", attack_data)

	return Status.SUCCESS
## Action to attack the enemy

var weapon_type: String = "torpedo"

func _init(weapon: String = "torpedo") -> void:
	super("Attack_" + weapon)
	weapon_type = weapon

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if not ctx.is_enemy_detected():
		return Status.FAILURE
	
	if not ctx.can_use_equipment(weapon_type):
		return Status.FAILURE
	
	if debug_enabled:
		print("AttackAction: Attacking with ", weapon_type, " at ", ctx.enemy_position)
	
	ctx.emit_event("combat_action", {
		"action": "attack",
		"weapon": weapon_type,
		"target": ctx.enemy_position
	})
	
	return Status.SUCCESS
