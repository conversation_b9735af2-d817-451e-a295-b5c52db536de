extends Node
class_name AIS<PERSON>

## Base AI Station Class
##
## This is the foundation class that all AI stations inherit from.
## It provides common functionality, behavior tree integration, and
## standardized interfaces that replace the old station implementations.
##
## Key Features:
## - Behavior tree integration
## - Event-driven communication
## - Parameter-based difficulty scaling
## - Standardized turn processing
## - Performance monitoring

## Core system references
var ai_personality: AIPersonality
var ai_event_bus: AIEventBus
var ai_behavior_manager: AIBehaviorManager

## Station identification
var station_name: String = ""
var station_type: String = ""

## Behavior tree system
var behavior_tree: BehaviorNode
var behavior_context: BehaviorContext
var tree_execution_result: BehaviorNode.Status = BehaviorNode.Status.FAILURE

## Turn management
var is_turn_active: bool = false
var is_turn_complete: bool = true
var turn_start_time: float = 0.0

## Performance tracking
var turn_count: int = 0
var total_turn_time: float = 0.0
var last_turn_time: float = 0.0

## Debug settings
var debug_mode: bool = false

func _init() -> void:
	# Set default station name from class name
	station_name = get_script().get_global_name().replace("AI", "").to_lower()
	station_type = station_name

func setup(personality: AIPersonality, event_bus: AIEventBus, behavior_manager: AIBehaviorManager) -> void:
	## Initialize the station with core AI systems
	
	ai_personality = personality
	ai_event_bus = event_bus
	ai_behavior_manager = behavior_manager
	
	# Create behavior tree
	behavior_tree = create_behavior_tree()
	
	# Register with behavior manager
	if ai_behavior_manager and behavior_tree:
		ai_behavior_manager.register_behavior_tree(station_name, behavior_tree, self)
	
	# Subscribe to relevant events
	_subscribe_to_events()
	
	# Initialize station-specific systems
	_initialize_station()
	
	if debug_mode:
		print("AIStation: ", station_name, " initialized")

## Core interface - must be implemented by subclasses

func create_behavior_tree() -> BehaviorNode:
	## Create the behavior tree for this station
	## This must be implemented by each station subclass
	
	push_warning("AIStation: create_behavior_tree not implemented in " + station_name)
	return null

func _initialize_station() -> void:
	## Initialize station-specific systems
	## Override in subclasses for custom initialization
	pass

## Turn management

func start_turn() -> void:
	## Start processing a new turn
	
	if is_turn_active:
		if debug_mode:
			print("AIStation: ", station_name, " turn already active")
		return
	
	is_turn_active = true
	is_turn_complete = false
	turn_start_time = Time.get_unix_time_from_system()
	turn_count += 1
	
	if debug_mode:
		print("AIStation: ", station_name, " starting turn ", turn_count)
	
	# Execute behavior tree
	_execute_turn()

func _execute_turn() -> void:
	## Execute the station's behavior tree for this turn
	
	if not behavior_tree or not ai_behavior_manager:
		_complete_turn(BehaviorNode.Status.FAILURE)
		return
	
	# Add reaction delay based on personality
	var reaction_delay = ai_personality.get_reaction_delay() if ai_personality else 1.0
	
	# Execute after delay
	await get_tree().create_timer(reaction_delay).timeout
	
	# Execute behavior tree
	tree_execution_result = ai_behavior_manager.execute_behavior_tree(station_name)
	
	# Complete turn based on result
	_complete_turn(tree_execution_result)

func _complete_turn(result: BehaviorNode.Status) -> void:
	## Complete the current turn
	
	if not is_turn_active:
		return
	
	is_turn_active = false
	is_turn_complete = true
	
	# Calculate turn time
	var turn_time = Time.get_unix_time_from_system() - turn_start_time
	last_turn_time = turn_time
	total_turn_time += turn_time
	
	# Handle turn result
	_handle_turn_result(result)
	
	if debug_mode:
		print("AIStation: ", station_name, " completed turn in ", "%.3f" % (turn_time * 1000), "ms with result: ", _status_to_string(result))

func _handle_turn_result(result: BehaviorNode.Status) -> void:
	## Handle the result of turn execution
	## Override in subclasses for custom result handling
	
	match result:
		BehaviorNode.Status.SUCCESS:
			_on_turn_success()
		BehaviorNode.Status.FAILURE:
			_on_turn_failure()
		BehaviorNode.Status.RUNNING:
			# This shouldn't happen for turn-based execution
			if debug_mode:
				print("AIStation: ", station_name, " behavior tree still running after turn completion")

func _on_turn_success() -> void:
	## Handle successful turn completion
	## Override in subclasses for custom success handling
	pass

func _on_turn_failure() -> void:
	## Handle failed turn completion
	## Override in subclasses for custom failure handling
	pass

func get_turn_complete_status() -> bool:
	## Check if the current turn is complete
	return is_turn_complete

## Event system

func _subscribe_to_events() -> void:
	## Subscribe to relevant events
	## Override in subclasses to subscribe to station-specific events
	
	if not ai_event_bus:
		return
	
	# Subscribe to common events
	ai_event_bus.subscribe(self, "game_state_updated", _on_game_state_updated)
	ai_event_bus.subscribe(self, "turn_started", _on_turn_started)
	ai_event_bus.subscribe(self, "turn_completed", _on_turn_completed)

func emit_station_event(event_type: String, data: Dictionary = {}) -> void:
	## Emit an event from this station
	
	if ai_event_bus:
		# Add station identification to event data
		var event_data = data.duplicate()
		event_data["station"] = station_name
		event_data["station_type"] = station_type
		
		ai_event_bus.emit_event(event_type, event_data)

## Personality and difficulty

func update_personality(new_personality: AIPersonality) -> void:
	## Update the station's personality parameters
	
	ai_personality = new_personality
	
	# Update behavior manager if available
	if ai_behavior_manager:
		ai_behavior_manager.update_personality(new_personality)
	
	# Handle personality change in subclass
	_on_personality_updated(new_personality)
	
	if debug_mode:
		print("AIStation: ", station_name, " personality updated")

func _on_personality_updated(new_personality: AIPersonality) -> void:
	## Handle personality updates
	## Override in subclasses for custom personality handling
	pass

func get_station_parameters() -> Dictionary:
	## Get station-specific parameters from personality
	
	if ai_personality:
		return ai_personality.get_station_parameters(station_type)
	return {}

## Status and monitoring

func get_status() -> Dictionary:
	## Get comprehensive station status
	
	return {
		"station_name": station_name,
		"station_type": station_type,
		"is_turn_active": is_turn_active,
		"is_turn_complete": is_turn_complete,
		"turn_count": turn_count,
		"last_turn_time": last_turn_time,
		"average_turn_time": (total_turn_time / turn_count) if turn_count > 0 else 0.0,
		"last_execution_result": _status_to_string(tree_execution_result),
		"behavior_tree_registered": ai_behavior_manager != null and behavior_tree != null,
		"personality_loaded": ai_personality != null
	}

func get_performance_stats() -> Dictionary:
	## Get performance statistics
	
	return {
		"turn_count": turn_count,
		"total_turn_time": total_turn_time,
		"last_turn_time": last_turn_time,
		"average_turn_time": (total_turn_time / turn_count) if turn_count > 0 else 0.0,
		"execution_result": _status_to_string(tree_execution_result)
	}

func reset_station() -> void:
	## Reset station state
	
	is_turn_active = false
	is_turn_complete = true
	tree_execution_result = BehaviorNode.Status.FAILURE
	
	# Reset behavior tree
	if behavior_tree:
		behavior_tree.reset()
	
	# Reset station-specific state
	_reset_station_state()
	
	if debug_mode:
		print("AIStation: ", station_name, " reset")

func _reset_station_state() -> void:
	## Reset station-specific state
	## Override in subclasses for custom reset behavior
	pass

## Event handlers

func _on_game_state_updated(event: Dictionary) -> void:
	## Handle game state updates
	## Override in subclasses for custom game state handling
	pass

func _on_turn_started(event: Dictionary) -> void:
	## Handle turn start events
	## Override in subclasses for custom turn start handling
	pass

func _on_turn_completed(event: Dictionary) -> void:
	## Handle turn completion events
	## Override in subclasses for custom turn completion handling
	pass

## Utility methods

func _status_to_string(status: BehaviorNode.Status) -> String:
	## Convert status enum to string for debugging
	
	match status:
		BehaviorNode.Status.SUCCESS:
			return "SUCCESS"
		BehaviorNode.Status.FAILURE:
			return "FAILURE"
		BehaviorNode.Status.RUNNING:
			return "RUNNING"
		_:
			return "UNKNOWN"

func enable_debug_mode(enable: bool = true) -> void:
	## Enable or disable debug mode
	
	debug_mode = enable
	
	if behavior_tree:
		behavior_tree.enable_debug(enable)
	
	if debug_mode:
		print("AIStation: ", station_name, " debug mode enabled")

func print_status() -> void:
	## Print current station status
	
	var status = get_status()
	print("=== ", station_name.capitalize(), " Station Status ===")
	print("Turn active: ", status.is_turn_active)
	print("Turn complete: ", status.is_turn_complete)
	print("Turn count: ", status.turn_count)
	print("Last turn time: ", "%.3f" % (status.last_turn_time * 1000), "ms")
	print("Average turn time: ", "%.3f" % (status.average_turn_time * 1000), "ms")
	print("Last result: ", status.last_execution_result)
	print("Behavior tree registered: ", status.behavior_tree_registered)

## Cleanup

func _exit_tree() -> void:
	## Clean up when station is removed
	
	# Unsubscribe from events
	if ai_event_bus:
		ai_event_bus.unsubscribe_all(self)
	
	# Unregister behavior tree
	if ai_behavior_manager:
		ai_behavior_manager.unregister_behavior_tree(station_name)
	
	if debug_mode:
		print("AIStation: ", station_name, " cleaned up")
