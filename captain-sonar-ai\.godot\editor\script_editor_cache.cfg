[res://MainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 16,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://game.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 220,
"scroll_position": 201.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://settings.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://help.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIProfile.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 38,
"scroll_position": 18.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://DraggableOverlay.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 3,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://MoveEntry.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://path_overlay.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 20,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://radio_station.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 49,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 9,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://SpectatorView.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 35,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 19,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://GameState.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 163,
"scroll_position": 153.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://PlayerSelection.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 22,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 225,
"scroll_position": 206.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://PLAYER_SELECTION_README.md]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "Markdown"
}

[res://SubmarineState.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 178,
"scroll_position": 177.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AICaptain.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 8,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 56,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIRadioOperator.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 24,
"scroll_position": 15.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIFirstMate.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 160,
"scroll_position": 140.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIEngineer.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 2,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 59,
"scroll_position": 40.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIPerformanceMonitor.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 51,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 60,
"scroll_position": 40.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
