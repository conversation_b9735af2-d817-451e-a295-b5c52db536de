class_name PatrolAction extends ActionNode

## Action to patrol an area

func _init(name: String = "Patrol") -> void:
	super(name)

func _execute_implementation(ctx: BehaviorContext) -> Status:
	## Execute patrol movement

	# Prepare patrol data
	var patrol_data = {
		"current_position": ctx.submarine_position,
		"patrol_type": "search",
		"speed": "normal"
	}

	# Emit patrol command event
	ctx.emit_event("patrol_command", patrol_data)

	return Status.SUCCESS
## Action to patrol in a pattern

var patrol_points: Array[Vector2i] = []
var current_patrol_index: int = 0

func _init(points: Array[Vector2i] = []) -> void:
	super("Patrol")
	patrol_points = points
	if patrol_points.is_empty():
		_generate_default_patrol()

func _execute_implementation(ctx: BehaviorContext) -> Status:
	if patrol_points.is_empty():
		# Fallback to random movement
		var random_move = RandomMoveAction.new()
		return random_move.execute(ctx)
	
	# Move toward next patrol point
	var target = patrol_points[current_patrol_index]
	var current_pos = ctx.submarine_position
	
	var direction = _get_direction_to_target(current_pos, target)
	
	# Check if we've reached the patrol point
	if current_pos == target:
		current_patrol_index = (current_patrol_index + 1) % patrol_points.size()
		target = patrol_points[current_patrol_index]
		direction = _get_direction_to_target(current_pos, target)
	
	if debug_enabled:
		print("PatrolAction: Moving ", direction, " toward patrol point ", target)
	
	ctx.emit_event("movement_decision", {"direction": direction})
	return Status.SUCCESS

func _generate_default_patrol() -> void:
	## Generate a default patrol pattern
	patrol_points = [
		Vector2i(5, 5),
		Vector2i(10, 5),
		Vector2i(10, 10),
		Vector2i(5, 10)
	]

func _get_direction_to_target(from: Vector2i, to: Vector2i) -> String:
	## Get the direction to move toward a target
	var dx = to.x - from.x
	var dy = to.y - from.y
	
	# Choose the direction with the largest difference
	if abs(dx) > abs(dy):
		return "EAST" if dx > 0 else "WEST"
	else:
		return "SOUTH" if dy > 0 else "NORTH"
